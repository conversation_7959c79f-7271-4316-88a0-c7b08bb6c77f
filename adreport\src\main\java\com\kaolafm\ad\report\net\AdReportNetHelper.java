package com.kaolafm.ad.report.net;

import android.util.Log;

import com.kaolafm.ad.report.AdReportAgent;
import com.kaolafm.ad.report.MonitorParameterManager;
import com.kaolafm.ad.report.api.ReportRequest;
import com.kaolafm.ad.report.api.monitor.MonitorRequest;
import com.kaolafm.ad.report.bean.AdReportMonitorEvent;
import com.kaolafm.ad.report.bean.AdReportPlayEndEvent;
import com.kaolafm.ad.report.bean.BaseAdEvent;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

public class AdReportNetHelper {

    private static final String TAG = AdReportNetHelper.class.getSimpleName();
    private ReportRequest mReportRequest;
    private MonitorRequest mMonitorRequest;

    private AdReportNetHelper(){
    }

    public static class KRADIO_AD_REPORT_NET_HELPER{
        private static final AdReportNetHelper INSTANCE = new AdReportNetHelper();
    }

    public static AdReportNetHelper getInstance(){
        return AdReportNetHelper.KRADIO_AD_REPORT_NET_HELPER.INSTANCE;
    }

    public void setReportRequest(ReportRequest reportRequest) {
        mReportRequest = reportRequest;
    }

    public void sendEvent(BaseAdEvent baseAdEvent, AdReportCallback adReportCallback){
        sendEventInternal(baseAdEvent,adReportCallback);
    }

    private void sendEventInternal(BaseAdEvent baseAdEvent, AdReportCallback adReportCallback){
        switch (baseAdEvent.getEventType()) {
            case AdReportAgent.EventType.PV:
                sendPV(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.CLICK:
                sendClick(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.DISPLAY_END:
                sendDisPlayEnd(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.DISPLAY_INTERRUPT:
                sendInterruptDisplay(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.DISPLAY_MORE_INTERACTION:
                sendDisplayMoreInteraction(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.MODE_INTERACTION_END:
                sendDisplayMoreInteractionEnd(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.PLAY_END:
                sendEndPlay(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.PLAY_START:
                sendStartPlay(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.SKIP:
                sendSkid(baseAdEvent,adReportCallback);
                break;
            case AdReportAgent.EventType.MIAOZHEN_MONITOR:
                sendMonitor(baseAdEvent,adReportCallback);
            case AdReportAgent.EventType.TALKING_DATA_MONITOR:
                sendMonitor(baseAdEvent,adReportCallback);
                break;
            default:
        }
    }

    private void sendMonitor(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        AdReportMonitorEvent adReportMonitorEvent = (AdReportMonitorEvent) baseAdEvent;
        String url = MonitorParameterManager.getInstance().getMonitorParameter(adReportMonitorEvent.getMonitorUrl(),baseAdEvent.getEventType());
        Log.i(TAG,"sendMonitor url:"+url);
        if (mMonitorRequest == null) {
            mMonitorRequest = new MonitorRequest();
        }
        mMonitorRequest.report(url,new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendPV(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        mReportRequest.display(baseAdEvent.getSessionId(),baseAdEvent.getDate(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendStartPlay(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        mReportRequest.play(baseAdEvent.getSessionId(),baseAdEvent.getDate(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendEndPlay(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        AdReportPlayEndEvent adReportPlayEndEvent = (AdReportPlayEndEvent) baseAdEvent;
        mReportRequest.endPlay(baseAdEvent.getSessionId(),adReportPlayEndEvent.getDate(), adReportPlayEndEvent.getPlayTime(),new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendClick(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        mReportRequest.click(baseAdEvent.getSessionId(), baseAdEvent.getDate(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendDisPlayEnd(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        mReportRequest.endDisplay(baseAdEvent.getSessionId(), baseAdEvent.getDate(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendSkid(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        mReportRequest.skip(baseAdEvent.getSessionId(), baseAdEvent.getDate(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendInterruptDisplay(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        mReportRequest.interruptDisplay(baseAdEvent.getSessionId(), baseAdEvent.getDate(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendDisplayMoreInteraction(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        mReportRequest.displayMoreInteraction(baseAdEvent.getSessionId(), baseAdEvent.getDate(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    private void sendDisplayMoreInteractionEnd(BaseAdEvent baseAdEvent, final AdReportCallback adReportCallback){
        mReportRequest.displayMoreInteractionEnd(baseAdEvent.getSessionId(), baseAdEvent.getDate(), new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                notifyCallback(adReportCallback,aBoolean);
            }

            @Override
            public void onError(ApiException exception) {
                notifyCallback(adReportCallback,false);
            }
        });
    }

    public interface AdReportCallback{

        void onResult(Boolean aBoolean);

    }

    private void notifyCallback(AdReportCallback adReportCallback,boolean result){
        if (adReportCallback != null) {
            adReportCallback.onResult(result);
        }
    }
}
