apply plugin: 'com.android.library'
apply plugin: 'greendao-plugin'
apply plugin: 'build-jar'
def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode and.adVersionCode
        versionName and.adVersionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-advert-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
    //配置数据库相关信息
    greendao {
        schemaVersion 1 //数据库版本号
        daoPackage 'com.kaolafm.ad.db.greendao'
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    compileOnly(dependent["rxlifecycle2-components"]) {
        exclude module: 'rxjava'
        exclude module: 'appcompat-v7'
    }
    annotationProcessor dependent['dagger2-compiler']
    embed project(path:":core", configuration:"default")
    embed project(path:":adreport", configuration:"default")
    annotationProcessor project(':buildSrc')
}

upload {
    sdkFlavor {
        includePackage = ['com/kaolafm/ad']
        proguardConfigFile = ["proguard-advert-rules.pro"]
        versionName = "*******"
        mavenConfig {
            artifactId      'ad'
            groupId         'com.kaolafm'
            libType         'jar'
            libDescription  'Advert'
            repository      'file:///Users/<USER>/AndroidStudioProjects/maven-repository'
            userName        'admin'
            password        'QA4A8saRBZFyuicF'
        }
    }
}