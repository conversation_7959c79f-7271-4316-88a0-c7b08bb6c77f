package com.kaolafm.opensdk.player.logic;

import android.content.Context;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.listener.IPlayerAsncStartExecutingListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.factory.PlayListControlFactory;
import com.kaolafm.opensdk.player.logic.listener.ICheckCopyrightListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerInteractionFiredListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.TimeDiscontinuousBroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playcontrol.PlayControl;
import com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.TVPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerListenerHelper;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;
import com.kaolafm.opensdk.player.logic.util.SDKReportManager;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;
import com.kaolafm.report.util.ReportConstants;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class PlayerManager {
    private static final String TAG = "PlayerManager";
    private IPlayListControl mIPlayListControl;
    private PlayControl mIPlayControl;
    /**
     * 当前播放的类型
     */
    private int mCustomType = PlayerConstants.RESOURCES_TYPE_INVALID;
    /**
     * 当前播放的对象
     */
    private PlayerBuilder mBuilder;

    public static Context mContext;

    /**
     * 自定义播单管理控制
     */
    private HashMap<Integer, Class<? extends IPlayListControl>> mCustomIPlayListControlHashMap;

    /**
     * 播放器监听管理
     */
    private PlayerListenerHelper mPlayerListenerHelper;

    /**
     * 是否正在进行seek操作
     */
    private boolean mSeekInProgress = false;

    /**
     * 用户在seek过程中是否点击了暂停，seek完成后需要保持暂停状态
     */
    private boolean mPendingPauseAfterSeek = false;

    private PlayerManager() {
        mPlayerListenerHelper = new PlayerListenerHelper();
        mBuilder = new PlayerBuilder();
        mCustomIPlayListControlHashMap = new HashMap<>();
    }

    private static class PlayManagerInstance {
        private static final PlayerManager PLAYERMANAGER = new PlayerManager();
    }

    public static PlayerManager getInstance() {
        return PlayManagerInstance.PLAYERMANAGER;
    }

    public void init(Context context) {
        PlayerLogUtil.log(getClass().getSimpleName(), "init", "");
        mContext = context;
        SDKReportManager.getInstance();
        ToneQualityHelper.getInstance().initToneSetValue(mContext);
        initPlayInitComplete();
        PlayControl.getInstance().init(context, mPlayerListenerHelper.getBasePlayStateListener(), mPlayerListenerHelper.getPlayerInitCompleteListener());
    }

    /**
     * 注册用户自定义播单控制
     *
     * @param type
     * @param iPlayListClass
     */
    public void registerCustomPlayList(int type, Class<? extends IPlayListControl> iPlayListClass) {
        if (PlayerPreconditions.checkNull(mCustomIPlayListControlHashMap)) {
            mCustomIPlayListControlHashMap = new HashMap<>();
        }
        if (type < PlayerConstants.RESOURCES_TYPE_MIN_SIZE) {
            PlayerPreconditions.checkArgument(false, "定义的播放类型必须大于" + PlayerConstants.RESOURCES_TYPE_MIN_SIZE);
        }

        mCustomIPlayListControlHashMap.put(type, iPlayListClass);
    }

    /**
     * 播放
     */
    public void play() {
        play(false);
    }

    /**
     * 播放
     *
     * @param fromUser 用户自主调用
     */
    public void play(boolean fromUser) {
        PlayerLogUtil.log(getClass().getSimpleName(), "play", "fromUser = " + fromUser);
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.play();
    }

    /**
     * bfang
     */
    public void playOnly() {
        PlayerLogUtil.log(getClass().getSimpleName(), "playOnly", "run");
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.playOnly();
    }

//    public void playBuilder(PlayItem playItem, boolean isPlayNow){
//        PlayerLogUtil.log(getClass().getSimpleName(), "playBuilder,processId= "+android.os.Process.myTid());
//        if(mBuilder!=null && mIPlayControl!=null){
//            mIPlayControl.start(mBuilder.getType(), playItem, isPlayNow);
//        }
//    }

    /**
     * 本方法只限于调用start方法中，PlayBuilder.isPlayNow=false执行之后，需要播放之后调用，不适用其他任何场合。
     */
    public void startDirectly(){
        if (mIPlayControl!=null){
            mIPlayControl.startDirectly();
        }
    }

    /**
     * 暂停
     */
    public void pause() {
        pause(false);
    }

    /**
     * 暂停
     *
     * @param fromUser 用户自主调用
     */
    public void pause(Boolean fromUser) {
        PlayerLogUtil.log(getClass().getSimpleName(), "pause", "fromUser = " + fromUser);
        mPlayerListenerHelper.setPauseFromUser(fromUser);

        // 如果正在seek过程中，标记用户暂停意图
        if (mSeekInProgress && fromUser) {
            mPendingPauseAfterSeek = true;
            PlayerLogUtil.log(getClass().getSimpleName(), "pause", "用户在seek期间暂停，在seek完成后保持暂停状态");
        }

        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.pause();
        mPlayerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PAUSED, getCurPlayItem());
    }

    /**
     * 停止
     */
    public void stop() {
        stop(false);
    }

    /**
     * 停止
     *
     * @param fromUser 用户自主调用
     */
    public void stop(boolean fromUser) {
        PlayerLogUtil.log(getClass().getSimpleName(), "stop: ", "fromUser = " + fromUser);
        mPlayerListenerHelper.setPauseFromUser(fromUser);

        // 停止时清理seek状态，避免状态残留
        mSeekInProgress = false;
        mPendingPauseAfterSeek = false;

        String reason;
        if (fromUser) {
            reason = ReportConstants.PLAY_CHANGE_BY_CLICK;
        } else {
            reason = ReportConstants.PLAY_CHANGE_BY_OTHER;
        }
        SDKReportManager.getInstance().reportEndPlay(reason, true);
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.stop();
    }

    public void reset() {
        reset(false);
    }

    public void reset(boolean fromUser) {
        PlayerLogUtil.log(getClass().getSimpleName(), "reset", "fromUser = " + fromUser);
        mPlayerListenerHelper.setPauseFromUser(fromUser);

        // 重置时清理seek状态，避免状态残留
        mSeekInProgress = false;
        mPendingPauseAfterSeek = false;

        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.reset();
    }

    /**
     * 切换播放状态
     */
    public void switchPlayerStatus() {
        switchPlayerStatus(false);
    }

    /**
     * 切换播放状态
     *
     * @param fromUser 是否是用户自主调用
     */
    public void switchPlayerStatus(boolean fromUser) {
        PlayerLogUtil.log(getClass().getSimpleName(), "switchPlayerStatus", "fromUser = " + fromUser);
        if (isPlaying()) {
            mPlayerListenerHelper.setPauseFromUser(fromUser);
        }
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.switchPlayerStatus();
    }


    /**
     * 快进
     *
     * @param position
     */
    public void seek(int position) {
        PlayerLogUtil.log(getClass().getSimpleName(), "seek", "position = " + position);

        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }

        // 设置seek状态标记
        mSeekInProgress = true;
        mPendingPauseAfterSeek = false;

        mIPlayControl.seek(position);
    }

    /**
     * 播放上一首
     */
    public void playPre() {
        PlayerLogUtil.log(getClass().getSimpleName(), "playPre");
        if (!NetworkUtil.isNetworkAvailable(PlayerManager.mContext)) {
            mPlayerListenerHelper.notifyPlayError(-1);
            return;
        }
        if (PlayerPreconditions.checkNull(mIPlayControl) || PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        mIPlayListControl.getPrePlayItem(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                if (PlayerPreconditions.checkNull(playItem)) {
                    mPlayerListenerHelper.notifyGetPlayListError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_PRE_ITEM_NULL, -1);
                    return;
                }
                mIPlayListControl.setCurPosition(playItem);
                mIPlayControl.start(mBuilder.getType(), playItem);
            }

            @Override
            public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(getClass().getSimpleName(), "playPre", "get pre error");
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
            }
        });
    }

    /**
     * 播放下一首
     */
    public void playNext() {
        PlayerLogUtil.log(getClass().getSimpleName(), "playNext");
        if (!NetworkUtil.isNetworkAvailable(PlayerManager.mContext)) {
            mPlayerListenerHelper.notifyPlayError(-1);
            return;
        }

        checkPlayListControl(mBuilder.getType());
        checkPlayControl();

        mIPlayListControl.getNextPlayItem(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                PlayerLogUtil.log(getClass().getSimpleName(), "playNext", "success"+ playItem.getTitle());
                if (PlayerPreconditions.checkNull(playItem)) {
                    mPlayerListenerHelper.notifyGetPlayListError(new InvalidPlayItem(), PlayerConstants.ERROR_CODE_PLAY_LIST_NEXT_ITEM_NULL, -1);
                    return;
                }
                mIPlayListControl.setCurPosition(playItem);
                mIPlayControl.start(mBuilder.getType(), playItem);
            }

            @Override
            public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(getClass().getSimpleName(), "playNext", "get next error");
                mIPlayControl.setInvalidPlayItem();
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
            }
        });
    }

    /**
     * 播放一个临时任务
     */
    public void startTempTask(TempTaskPlayItem playItem) {
        if (PlayerPreconditions.checkNull(playItem)) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "startTempTask", "playurl = " + playItem.getPlayUrl());
        checkPlayControl();
        mIPlayControl.startTempTask(playItem);
    }

    /**
     * 停止一个临时任务
     */
    public void stopTempTask() {
        stopTempTask(true);
    }

    /**
     * 停止一个临时任务
     *
     * @param continueStartPlayItem 继续播放碎片
     */
    public void stopTempTask(boolean continueStartPlayItem) {
        checkPlayControl();
        mIPlayControl.stopTempTask(continueStartPlayItem);
    }

    /**
     * 播放播放列表里面的数据
     *
     * @param playItem
     */
    public void startPlayItemInList(PlayItem playItem) {
        if (playItem == null) {
            return;
        }
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        if (mIPlayListControl instanceof BroadcastPlayListControl && playItem instanceof TimeDiscontinuousBroadcastPlayItem && !mIPlayListControl.isExistPlayItem(playItem)) {
            //如果是广播补全的节目，则需要额外判断开始时间
            return;
        } else if (!mIPlayListControl.isExistPlayItem(playItem.getAudioId())) {
            //其他节目或碎片，只需要判断audioId
            return;
        }
        startPlayItem(playItem);
    }

    /**
     * 开始播放
     *
     * @param builder
     */
    public void start(PlayerBuilder builder) {
        if (PlayerPreconditions.checkNull(builder)) {
            return;
        }

        checkPlayListControl(builder.getType());
        checkPlayControl();

        PlayItem playItem = mIPlayListControl.getPlayItem(builder);
        if (playItem != null) {
            startPlayItem(builder, playItem);
        } else {
            startNewBuilder(builder);
        }
    }

    /**
     * 开始播放播单补全的广播节目
     */
    public void startTimeDiscontinuousBroadcastPlayItem(TimeDiscontinuousBroadcastPlayItem playItem) {
        if (PlayerPreconditions.checkNull(playItem) || PlayerPreconditions.checkNull(mBuilder) || PlayerPreconditions.checkNull(mIPlayListControl) || PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        if (!mBuilder.getId().equals(String.valueOf(playItem.getRadioId()))) {
            PlayerLogUtil.log(getClass().getSimpleName(), "startTimeDiscontinuousBroadcastPlayItem", "The program is not owned by the current radio!");
            return;
        }
        if (!mIPlayListControl.isExistPlayItem(playItem)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "startTimeDiscontinuousBroadcastPlayItem", "This program does not belong to the current playlist!");
            return;
        }
        startPlayItem(playItem);
    }

    /**
     * 禁用淡入淡出效果
     */
    public void disableAudioFade() {
        checkPlayControl();
        if (mIPlayControl != null) {
            mIPlayControl.disableAudioFade();
        }
    }

    public void setHttpProxy(String httpProxy) {
        mIPlayControl.setHttpProxy(httpProxy);
    }

    public void clearHttpProxy() {
        mIPlayControl.clearHttpProxy();
    }

    // 清除dns缓存
    public void clearDnsCache(boolean clearDnsCache) {
        mIPlayControl.clearDnsCache(clearDnsCache);
    }

    /**
     * 设置淡入淡出效果配置
     *
     * @param audioFadeConfig
     */
    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
        checkPlayControl();
        if (mIPlayControl != null) {
            mIPlayControl.setAudioFadeConfig(audioFadeConfig);
        }
    }

    /**
     * 校验 PlayListControl
     *
     * @param type
     */
    private void checkPlayListControl(int type) {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            initCustomPlayListControl(type);
            if (PlayerPreconditions.checkNull(mIPlayListControl)) {
                mIPlayListControl = PlayListControlFactory.getPlayListControl(type);
                mIPlayListControl.setCallback(mPlayerListenerHelper.getPlayListStateListener());
            }
        }
    }

    /**
     * 校验playControl
     */
    private void checkPlayControl() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            mIPlayControl = PlayControl.getInstance();
        }
    }

    /**
     * 开始播放
     *
     * @param playItem
     */
    private void startPlayItem(PlayItem playItem) {
        mIPlayListControl.setCurPosition(playItem);
        mIPlayControl.start(mBuilder.getType(), playItem);
    }

    /**
     * 开始播放
     *
     * @param builder
     * @param playItem
     */
    private void startPlayItem(PlayerBuilder builder, PlayItem playItem) {
        mIPlayListControl.setCurPosition(playItem);
        long position = getSeekPosition(builder);
        if (position > 0) {
            playItem.setPosition((int) position);
        }
        mIPlayControl.start(mBuilder.getType(), playItem, builder.isPlayNow());
    }

    /**
     * 开始一个新的builder播放, 用于
     *
     * @param builder
     */
    private void startNewBuilder(PlayerBuilder builder) {
        if (builder.getType() == PlayerConstants.RESOURCES_TYPE_INVALID) {
            return;
        }
        IPlayListControl tempIPlayListControl;

        if (builder.getType() == mCustomType) {
            PlayerLogUtil.log(getClass().getSimpleName(), "startNewBuilder", "the same type");
            tempIPlayListControl = mIPlayListControl;
        } else {
            PlayerLogUtil.log(getClass().getSimpleName(), "startNewBuilder", "different type");
            tempIPlayListControl = PlayListControlFactory.getPlayListControl(builder.getType());
        }

        checkCopyright(builder, tempIPlayListControl, new ICheckCopyrightListener() {
            @Override
            public void onGranted() {
                if (builder.getType() == mCustomType) {
                    PlaylistInfo playlistInfo = mIPlayListControl.getPlayListInfo();
                    playlistInfo.setTempId(builder.getId());
                    if (builder instanceof CustomPlayerBuilder) {
                        playlistInfo.setTempChildId(((CustomPlayerBuilder) builder).getChildId());
                    }
                }
                if (mIPlayListControl != tempIPlayListControl) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "startNewBuilder", "get play list success");
                    mIPlayListControl.release();
                    mIPlayListControl = tempIPlayListControl;
                    mIPlayListControl.setCallback(mPlayerListenerHelper.getPlayListStateListener());
                }

                initBuilder(builder);

                tempIPlayListControl.initPlayList(builder, new IPlayListGetListener() {
                    @Override
                    public void onDataGet(PlayItem playItem, List<PlayItem> playItemArrayList) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "startNewBuilder", "get play list success");

                        mIPlayListControl.setCurPosition(playItem);
                        long position = getSeekPosition(mBuilder);
                        if (position > 0) {
                            playItem.setPosition((int) position);
                        }
                        mIPlayControl.start(mBuilder.getType(), playItem, builder.isPlayNow());
                    }

                    @Override
                    public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "startNewBuilder", "get play list error");
                        mIPlayListControl.setCurPosition(playItem);
                        mIPlayControl.start(mBuilder.getType(), playItem, builder.isPlayNow());
                        initErrorBuilder(mBuilder);
                        mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
                    }
                });
            }

            @Override
            public void onError(PlayItem playItem, int errorCode, int errorExtra) {
                PlayerLogUtil.log(getClass().getSimpleName(), "startNewBuilder", "check copyright error: errorcode=" + errorCode + " ,errorextra=" + errorExtra);
                mPlayerListenerHelper.notifyGetPlayListError(playItem, errorCode, errorExtra);
                if (errorCode != PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE) {
                    //按照以前的逻辑，只要是已经授权了，哪怕是请求数据有问题，也要接着走流程,现在保持逻辑不变
                    continueOnError(builder, tempIPlayListControl, playItem);
                }
            }
        });
    }

    /**
     * 当检查简版出错后，继续按照以前的逻辑走流程
     * 按照以前的逻辑，只要是已经授权了，哪怕是请求数据有问题，也要接着走流程,现在保持逻辑不变
     *
     * @param builder
     * @param tempIPlayListControl
     * @param playItem
     */
    private void continueOnError(PlayerBuilder builder, IPlayListControl tempIPlayListControl, PlayItem playItem) {
        if (builder.getType() == mCustomType) {
            PlaylistInfo playlistInfo = mIPlayListControl.getPlayListInfo();
            playlistInfo.setTempId(builder.getId());
            if (builder instanceof CustomPlayerBuilder) {
                playlistInfo.setTempChildId(((CustomPlayerBuilder) builder).getChildId());
            }
        }
        if (mIPlayListControl != tempIPlayListControl) {
            PlayerLogUtil.log(getClass().getSimpleName(), "startNewBuilder", "get play list success");
            mIPlayListControl.release();
            mIPlayListControl = tempIPlayListControl;
            mIPlayListControl.setCallback(mPlayerListenerHelper.getPlayListStateListener());
        }

        initBuilder(builder);

        mIPlayListControl.setCurPosition(playItem);
        mIPlayControl.start(mBuilder.getType(), playItem);
        initErrorBuilder(mBuilder);
    }

    /**
     * 校验广播/电视的版权
     *
     * @param builder
     * @param tempIPlayListControl
     * @param iCheckCopyrightListener
     */
    private void checkCopyright(PlayerBuilder builder, IPlayListControl tempIPlayListControl, ICheckCopyrightListener iCheckCopyrightListener) {
        if (tempIPlayListControl instanceof BroadcastPlayListControl) {
            ((BroadcastPlayListControl) tempIPlayListControl).checkCopyright(builder, iCheckCopyrightListener);
        } else if (tempIPlayListControl instanceof TVPlayListControl) {
            ((TVPlayListControl) tempIPlayListControl).checkCopyright(builder, iCheckCopyrightListener);
        } else if (iCheckCopyrightListener != null) {
            iCheckCopyrightListener.onGranted();
        }
    }
    /**
     * 校验广播/电视的版权
     *
     * @param builder
     * @param iCheckCopyrightListener
     */
    public void checkCopyright(PlayerBuilder builder, ICheckCopyrightListener iCheckCopyrightListener) {
        if (builder.getType() == PlayerConstants.RESOURCES_TYPE_INVALID) {
            return;
        }
        IPlayListControl tempIPlayListControl;

        if (builder.getType() == mCustomType) {
            PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "the same type");
            tempIPlayListControl = mIPlayListControl;
        } else {
            PlayerLogUtil.log(getClass().getSimpleName(), "checkCopyright", "different type");
            tempIPlayListControl = PlayListControlFactory.getPlayListControl(builder.getType());
        }
        checkCopyright(builder, tempIPlayListControl, iCheckCopyrightListener);
    }

    private void initErrorBuilder(PlayerBuilder builder) {
        if (builder.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST
                || builder.getType() == PlayerConstants.RESOURCES_TYPE_TV) {
            pause();
            mIPlayControl.stopTimer();
        }
    }

    /**
     * 是否有下一页数据
     *
     * @return
     */
    public boolean hasNextPage() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return false;
        }
        return mIPlayListControl.hasNextPage();
    }

    /**
     * 是否有上一页数据
     *
     * @return
     */
    public boolean hasPrePage() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return false;
        }
        return mIPlayListControl.hasPrePage();
    }

    /**
     * 获取当前播放的playitem
     *
     * @return
     */
    public PlayItem getCurPlayItem() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return new InvalidPlayItem();
        }
        return mIPlayControl.getCurrentPlayItem();
    }

    /**
     * 获取当前播放的在播单位置
     *
     * @return
     */
    public int getPlayListCurrentPosition() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return -1;
        }
        return mIPlayListControl.getCurPosition();
    }

    /**
     * 是否有上一首
     *
     * @return
     */
    public boolean hasPre() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return false;
        }
        return mIPlayListControl.hasPre();
    }

    /**
     * 是否有下一首
     *
     * @return
     */
    public boolean hasNext() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return false;
        }
        return mIPlayListControl.hasNext();
    }


    /**
     * 请求音频焦点
     *
     * @return
     */
    public boolean requestAudioFocus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            mIPlayControl = PlayControl.getInstance();
        }
        return mIPlayControl.requestAudioFocus();
    }


    /**
     * 释放音频焦点
     *
     * @return
     */
    public boolean abandonAudioFocus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            mIPlayControl = PlayControl.getInstance();
        }
        return mIPlayControl.abandonAudioFocus();
    }


    /**
     * 获取播单
     *
     * @return
     */
    public List<PlayItem> getPlayList() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return null;
        }
        return mIPlayListControl.getPlayList();
    }

    /**
     * 加载下一页数据
     *
     * @param iPlayListGetListener
     */
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        mIPlayListControl.loadNextPage(iPlayListGetListener);
    }

    /**
     * 加载上一页数据
     *
     * @param iPlayListGetListener
     */
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        mIPlayListControl.loadPrePage(iPlayListGetListener);
    }

    public void loadPageData(long audioId, int pageNum, IPlayListGetListener iPlayListGetListener) {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return;
        }
        mIPlayListControl.loadPageData(AlbumPlayListControl.LOAD_DATA_PAGE,
                audioId, pageNum, iPlayListGetListener);
    }

    /**
     * 获取当前播单信息
     *
     * @return
     */
    public PlaylistInfo getPlayListInfo() {
        if (PlayerPreconditions.checkNull(mIPlayListControl)) {
            return null;
        }
        return mIPlayListControl.getPlayListInfo();
    }

    /**
     * 获取播单控制
     *
     * @return
     */
    public IPlayListControl getPlayListControl() {
        return mIPlayListControl;
    }

    /**
     * 设置是否支持音量均衡
     *
     * @param isActive
     */
    public void setLoudnessNormalization(int isActive) {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.setLoudnessNormalization(isActive);
    }

    /**
     * 获取当前操作是否是用户自主获取
     *
     * @return
     */
    public boolean isPauseFromUser() {
        return mPlayerListenerHelper.isPauseFromUser();
    }

    private void initBuilder(PlayerBuilder playerBuilder) {
        if (PlayerPreconditions.checkNull(playerBuilder)) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "initBuilder: ");
        if (playerBuilder.getType() != PlayerConstants.RESOURCES_TYPE_INVALID) {
            mBuilder = playerBuilder;
            mCustomType = mBuilder.getType();
            PlayerLogUtil.log(getClass().getSimpleName(), "initBuilder", "type = " + mCustomType + " id = " + mBuilder.getId());
        }
    }

    private void initCustomPlayListControl(int type) {
        if (!ListUtil.isEmpty(mCustomIPlayListControlHashMap)) {
            Class<? extends IPlayListControl> tempClass = mCustomIPlayListControlHashMap.get(type);
            if (!PlayerPreconditions.checkNull(tempClass)) {
                try {
                    mIPlayListControl = tempClass.newInstance();
                    mIPlayListControl.setCallback(mPlayerListenerHelper.getPlayListStateListener());
                } catch (Exception e) {

                }
            }
        }
    }

    private long getSeekPosition(PlayerBuilder playerBuilder) {
        if (playerBuilder instanceof CustomPlayerBuilder) {
            CustomPlayerBuilder customPlayerBuilder = (CustomPlayerBuilder) playerBuilder;
            return customPlayerBuilder.getSeekPosition();
        }
        return 0;
    }

    public boolean isPlayerInitSuccess() {
        return mPlayerListenerHelper.isPlayerInitSuccess();
    }

    public boolean isPlaying() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return false;
        }
        return mIPlayControl.isPlaying();
    }

    public int getPlayStatus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return PlayerConstants.TYPE_PLAYER_IDLE;
        }
        return mIPlayControl.getPlayStatus();
    }

    public void setLogInValid() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.setLogInValid();
    }

    public void enableSDKPlayerLog() {
        PlayerLogUtil.enableLog();
    }

    public void disableSDKPlayerLog() {
        PlayerLogUtil.disableLog();
    }

    public long getCurrentPlayPlace() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return -1;
        }
        return mIPlayControl.getCurrentPosition();
    }

    public void setMediaVolume(float leftVolume, float rightVolume) {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.setMediaVolume(leftVolume, rightVolume);
    }

    public int getCurrentAudioFocusStatus() {
        return mPlayerListenerHelper.getAudioFocusStatus();
    }

    /**
     * 添加播单改变监听
     *
     * @param iPlayListControlListener
     */
    public void addPlayListControlStateCallback(IPlayListStateListener iPlayListControlListener) {
        mPlayerListenerHelper.addPlayListControlStateCallback(iPlayListControlListener);
    }

    /**
     * 删除播单改变监听
     *
     * @param iPlayListControlListener
     */
    public void removePlayListControlStateCallback(IPlayListStateListener iPlayListControlListener) {
        mPlayerListenerHelper.removePlayListControlStateCallback(iPlayListControlListener);
    }

    /**
     * 添加播放状态监听
     *
     * @param iPlayControlListener
     */
    public void addPlayControlStateCallback(IPlayerStateListener iPlayControlListener) {
        mPlayerListenerHelper.addPlayControlStateCallback(iPlayControlListener);
    }

    /**
     * 删除播放状态监听
     *
     * @param iPlayControlListener
     */
    public void removePlayControlStateCallback(IPlayerStateListener iPlayControlListener) {
        mPlayerListenerHelper.removePlayControlStateCallback(iPlayControlListener);
    }

    /**
     * 添加音频焦点状态监听
     *
     * @param iAudioFocusListener
     */
    public void addAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        mPlayerListenerHelper.addAudioFocusListener(iAudioFocusListener);
    }

    /**
     * 删除音频焦点状态监听
     *
     * @param iAudioFocusListener
     */
    public void removeAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        mPlayerListenerHelper.removeAudioFocusListener(iAudioFocusListener);
    }

    /**
     * 添加播放器初始化完成监听
     *
     * @param iPlayerInitCompleteListener
     */
    public void addPlayerInitComplete(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        mPlayerListenerHelper.addPlayerInitComplete(iPlayerInitCompleteListener);
    }

    /**
     * 删除播放器初始化完成监听
     *
     * @param iPlayerInitCompleteListener
     */
    public void removePlayerInitComplete(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        mPlayerListenerHelper.removePlayerInitComplete(iPlayerInitCompleteListener);
    }

    /**
     * 添加播放器通用回调
     *
     * @param iGeneralListener
     */
    public void addGeneralListener(IGeneralListener iGeneralListener) {
        mPlayerListenerHelper.addGeneralListener(iGeneralListener);
    }

    /**
     * 删除播放器通用回调
     *
     * @param generalListener
     */
    public void removeGeneralListener(IGeneralListener generalListener) {
        mPlayerListenerHelper.removeGeneralListener(generalListener);
    }


    public void setPlayerInteractionFiredListener(IPlayerInteractionFiredListener interactionFiredListener) {
        checkPlayControl();
        mIPlayControl.setPlayerInteractionFiredListener(interactionFiredListener);
    }

    public void setPlayerAsncStartExecutingListener(IPlayerAsncStartExecutingListener mPlayerAsncStartExecutingListener) {
        checkPlayControl();
        mIPlayControl.setPlayerAsncStartExecutingListener(mPlayerAsncStartExecutingListener);
    }

    public int getCustomType() {
        if (mBuilder != null) {
            return mBuilder.getType();
        }
        return PlayerConstants.RESOURCES_TYPE_INVALID;
    }

    public PlayItem getCurrentTempTaskPlayItem() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return null;
        }
        return mIPlayControl.getCurrentTempTaskPlayItem();
    }

    /**
     * destroy
     */
    public void destroy() {
        if (!PlayerPreconditions.checkNull(mIPlayControl)) {
            mIPlayControl.destroy();
        }
        mPlayerListenerHelper.release();
    }

    private void initPlayInitComplete() {
        mPlayerListenerHelper.addPlayerInitComplete(flag -> {
            checkPlayControl();
            PlayControl.getInstance().setAudioFocusListener(mPlayerListenerHelper.getOnAudioFocusChangeInter());
            PlayerCustomizeManager.getInstance().registerAudioStateChangedByAudioFocusListener(mPlayerListenerHelper.getAudioStateChangedByAudioFocusListener());
        });
    }


    public void notifyAudioFocus(int state) {
        mPlayerListenerHelper.notifyAudioFocus(state);
    }

    public boolean isAsyncStartExecuting() {
        if (!PlayerPreconditions.checkNull(mIPlayControl)) {
            return mIPlayControl.isAsyncStartExecuting();
        }
        return false;
    }

    /**
     * 通过audio请求数据拿到playItem
     *
     * @param audio
     * @return
     */
    public void getPlayItemFromAudioId(long audio, GetPlayItemListener listener) {
        new AudioRequest().getAudioDetails(audio, new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails audioDetails) {
                PlayItem albumPlayItem = PlayListUtils.translateAlbumToPlayItem(audioDetails);
                listener.success(albumPlayItem);
            }

            @Override
            public void onError(ApiException exception) {
                listener.error(exception);
            }
        });
    }

    public interface GetPlayItemListener {
        void success(PlayItem playitem);

        void error(ApiException exception);
    }


    /**
     * 用户状态变化或支付状态变化时重新拉取播单时调用
     */
    public void resetPlayListControl() {
        if (mIPlayListControl != null) {
            mIPlayListControl.clearPlayList();
            mIPlayListControl = null;
        }
        mCustomIPlayListControlHashMap = null;
        reset(false);
    }

    /**
     * 设置AudioTrack的AudioAttributes的参数Usage和ContentType
     * 备注：在prepare()函数之前调用
     * @param usage 场景
     * @param contentType 内容类型
     */
    public void setUsageAndContentType(int usage, int contentType) {
        checkPlayControl();
        mIPlayControl.setUsageAndContentType(usage, contentType);
    }

    /**
     * 处理seek完成事件 - 用于解决seek过程中暂停状态管理
     * 此方法由PlayerListenerHelper调用
     */
    public void handleSeekComplete(PlayItem playItem) {
        PlayerLogUtil.log(getClass().getSimpleName(), "handleSeekComplete",
            "mSeekInProgress=" + mSeekInProgress + ", mPendingPauseAfterSeek=" + mPendingPauseAfterSeek);

        if (mSeekInProgress) {
            mSeekInProgress = false;

            // 如果用户在seek过程中暂停了，需要保持暂停状态
            if (mPendingPauseAfterSeek) {
                PlayerLogUtil.log(getClass().getSimpleName(), "handleSeekComplete",
                    "用户在seek期间暂停，延迟执行暂停以确保在IJK自动播放之后");

                // 标记需要在播放回调时拦截，不使用延迟方案
                PlayerLogUtil.log(getClass().getSimpleName(), "handleSeekComplete",
                    "标记拦截播放回调，将在onPlayerPlaying时处理");
            }
        }
    }

    /**
     * 检查是否需要拦截播放状态回调
     * 此方法用于在播放状态回调时进行拦截，防止seek后的自动播放
     */
    public boolean shouldInterceptPlayingCallback() {
        boolean shouldIntercept = mPendingPauseAfterSeek;
        if (shouldIntercept) {
            PlayerLogUtil.log(getClass().getSimpleName(), "shouldInterceptPlayingCallback",
                "拦截播放回调，用户在seek期间暂停，立即执行暂停");

            // 立即暂停并清除标记
            if (mIPlayControl != null) {
                mIPlayControl.pause();
                mPlayerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PAUSED, getCurPlayItem());
            }
            mPendingPauseAfterSeek = false;
        }
        return shouldIntercept;
    }
}
