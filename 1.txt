10:32:32.682947 11844 11844 D service_ytmusic: Received session metadata: 5789838513649298541
10:32:32.725304 11844 11844 D CompatibilityChangeReporter: Compat change id reported: 171979766; UID 10040; state: ENABLED
10:32:32.781911 11844 11844 V GraphicsEnvironment: ANGLE Developer option for 'com.voyah.cockpit.voyahmusic' set to: 'default'
10:32:32.782806 11844 11844 V GraphicsEnvironment: ANGLE GameManagerService for com.voyah.cockpit.voyahmusic: false
10:32:32.782928 11844 11844 V GraphicsEnvironment: Neither updatable production driver nor prerelease driver is supported.
10:32:32.790487 11844 11844 D NetworkSecurityConfig: No Network Security Config specified, using platform default
10:32:32.790724 11844 11844 D NetworkSecurityConfig: No Network Security Config specified, using platform default
10:32:32.795815 11844 11844 D ViewCmdAspectProcessor: onApplicationCreateAround, AOP=class com.voyah.media.app.MusicApp.onCreate
10:32:32.796914 11844 11844 D VoiceViewCmdUtils: packageName:com.voyah.cockpit.voyahmusic, processName:com.voyah.cockpit.voyahmusic:service_ytmusic
10:32:32.797850 11844 11844 I MMKV    : Disable checkProcessMode()
10:32:32.799190 11844 11844 I MMKV    : current API level = 33, libc++_shared=0
10:32:32.799220 11844 11844 I MMKV    : <MMKV.cpp:166::initialize> version v1.3.9, page size 4096, arch arm64-v8a
10:32:32.799230 11844 11844 I MMKV    : <MMKV.cpp:177::initialize> armv8 AES instructions is supported
10:32:32.799239 11844 11844 I MMKV    : <MMKV.cpp:185::initialize> armv8 CRC32 instructions is supported
10:32:32.799278 11844 11844 I MMKV    : <MMKV.cpp:225::initializeMMKV> root dir: /data/user/0/com.voyah.cockpit.voyahmusic/files/mmkv
10:32:32.800561 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [MusicApp user0]  MusicApp initOnMain processName = com.voyah.cockpit.voyahmusic:service_ytmusic
10:32:32.802658 11844 11844 D MegaJankMonitor: u0 uid 10040 pid 11844 pkg:com.voyah.cockpit.voyahmusic
10:32:32.802658 11844 11844 D MegaJankMonitor: com.voyah.cockpit.voyahmusic:service_ytmusic initialized successfully!
10:32:32.824455 11844 11844 D CompatibilityChangeReporter: Compat change id reported: 183155436; UID 10040; state: ENABLED
10:32:32.867780 11844 11844 I Router  : inject---className:AbsMediaCommonApi
10:32:32.867832 11844 11844 I Router  : inject---className:AbsMediaCommonApi mMapping classNameAbsMediaCommonApi
10:32:32.880120 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [MultiUserUtils user0] MultiUserUtils isMultiUser : true
10:32:32.885142 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] init CarAudioFocusManager car ready : true
10:32:32.885995 11844 11844 D CAR.L   : registerCarAudioFocusChangeCallback com.voyah.cockpit.voyahmusic
10:32:32.959332 11844 11844 I Router  : inject---className:YtCacheListSM
10:32:32.959357 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameAbsMediaCommonApi
10:32:32.959368 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameBtMusicVoiceSM
10:32:32.959382 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameBtMusicSM
10:32:32.959391 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameUsbMusicVoiceSM
10:32:32.959400 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameUsbMusicSM
10:32:32.959410 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameUsbMediaControllerSM
10:32:32.959419 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameUsbSearchSM
10:32:32.959427 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameXmlyRecommendDataSM
10:32:32.959436 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameXmlyMusicVoiceSM
10:32:32.959445 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameXmlyClassifySM
10:32:32.959453 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameXmlySearchSM
10:32:32.959462 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameXmlyMineSM
10:32:32.959474 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQMultiDeviceTransformPlaySM
10:32:32.959483 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQMusicSM
10:32:32.959491 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQSearchSM
10:32:32.959501 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQRankSM
10:32:32.959509 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQFolderSM
10:32:32.959518 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQCacheListSM
10:32:32.959527 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQSingerSM
10:32:32.959536 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQMusicVoiceSM
10:32:32.959545 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQAiDjSM
10:32:32.959553 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQReportSM
10:32:32.959564 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQAlbumSM
10:32:32.959573 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQSectionSM
10:32:32.959581 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameQQRecommendSM
10:32:32.959590 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWyMusicRecommendSM
10:32:32.959599 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWyHighQualitySM
10:32:32.959608 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWyMusicSearchSM
10:32:32.959617 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWyMultiDeviceTransformPlaySM
10:32:32.959625 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWyMusicSingerSM
10:32:32.959634 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWyMusicUserSM
10:32:32.959643 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWySoundEffectSM
10:32:32.959654 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWyAiDjSM
10:32:32.959664 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameWyMusicVoiceSM
10:32:32.959672 11844 11844 I Router  : inject---className:YtCacheListSM mMapping classNameYtCacheListSM
10:32:32.959752 11844 11844 I Router  : inject---className:YtMusicSM
10:32:32.959767 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameAbsMediaCommonApi
10:32:32.959777 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameBtMusicVoiceSM
10:32:32.959786 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameBtMusicSM
10:32:32.959795 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameUsbMusicVoiceSM
10:32:32.959813 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameUsbMusicSM
10:32:32.959823 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameUsbMediaControllerSM
10:32:32.959832 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameUsbSearchSM
10:32:32.959841 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameXmlyRecommendDataSM
10:32:32.959850 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameXmlyMusicVoiceSM
10:32:32.959859 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameXmlyClassifySM
10:32:32.959868 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameXmlySearchSM
10:32:32.959877 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameXmlyMineSM
10:32:32.959886 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQMultiDeviceTransformPlaySM
10:32:32.959895 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQMusicSM
10:32:32.959906 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQSearchSM
10:32:32.959916 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQRankSM
10:32:32.959924 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQFolderSM
10:32:32.959933 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQCacheListSM
10:32:32.959942 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQSingerSM
10:32:32.959951 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQMusicVoiceSM
10:32:32.959959 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQAiDjSM
10:32:32.959968 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQReportSM
10:32:32.959976 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQAlbumSM
10:32:32.959985 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQSectionSM
10:32:32.959994 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameQQRecommendSM
10:32:32.960005 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWyMusicRecommendSM
10:32:32.960014 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWyHighQualitySM
10:32:32.960023 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWyMusicSearchSM
10:32:32.960173 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWyMultiDeviceTransformPlaySM
10:32:32.960186 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWyMusicSingerSM
10:32:32.960309 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWyMusicUserSM
10:32:32.960325 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWySoundEffectSM
10:32:32.960335 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWyAiDjSM
10:32:32.960346 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameWyMusicVoiceSM
10:32:32.960360 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameYtCacheListSM
10:32:32.960376 11844 11844 I Router  : inject---className:YtMusicSM mMapping classNameYtMusicSM
10:32:32.960542 11844 11844 I Router  : inject---className:YtMusicVoiceSM
10:32:32.960559 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameAbsMediaCommonApi
10:32:32.960569 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameBtMusicVoiceSM
10:32:32.960578 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameBtMusicSM
10:32:32.960587 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameUsbMusicVoiceSM
10:32:32.960596 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameUsbMusicSM
10:32:32.960610 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameUsbMediaControllerSM
10:32:32.960619 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameUsbSearchSM
10:32:32.960628 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameXmlyRecommendDataSM
10:32:32.960638 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameXmlyMusicVoiceSM
10:32:32.960647 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameXmlyClassifySM
10:32:32.960656 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameXmlySearchSM
10:32:32.960665 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameXmlyMineSM
10:32:32.960674 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQMultiDeviceTransformPlaySM
10:32:32.960683 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQMusicSM
10:32:32.960694 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQSearchSM
10:32:32.960703 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQRankSM
10:32:32.960712 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQFolderSM
10:32:32.960721 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQCacheListSM
10:32:32.960730 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQSingerSM
10:32:32.960739 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQMusicVoiceSM
10:32:32.960748 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQAiDjSM
10:32:32.960761 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQReportSM
10:32:32.960770 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQAlbumSM
10:32:32.960781 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQSectionSM
10:32:32.960792 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameQQRecommendSM
10:32:32.960801 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWyMusicRecommendSM
10:32:32.960883 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWyHighQualitySM
10:32:32.960931 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWyMusicSearchSM
10:32:32.960943 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWyMultiDeviceTransformPlaySM
10:32:32.960954 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWyMusicSingerSM
10:32:32.960964 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWyMusicUserSM
10:32:32.960974 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWySoundEffectSM
10:32:32.960984 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWyAiDjSM
10:32:32.961075 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameWyMusicVoiceSM
10:32:32.961094 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameYtCacheListSM
10:32:32.961107 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameYtMusicSM
10:32:32.961117 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameYtMusicPlayerSM
10:32:32.961127 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameYtLoginSM
10:32:32.961137 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameYtRecommendSM
10:32:32.961147 11844 11844 I Router  : inject---className:YtMusicVoiceSM mMapping classNameYtMusicVoiceSM
10:32:32.961319 11844 11844 I Router  : inject---className:YtLoginSM
10:32:32.961340 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameAbsMediaCommonApi
10:32:32.961351 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameBtMusicVoiceSM
10:32:32.961390 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameBtMusicSM
10:32:32.961411 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameUsbMusicVoiceSM
10:32:32.961422 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameUsbMusicSM
10:32:32.961432 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameUsbMediaControllerSM
10:32:32.961441 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameUsbSearchSM
10:32:32.961452 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameXmlyRecommendDataSM
10:32:32.961462 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameXmlyMusicVoiceSM
10:32:32.961472 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameXmlyClassifySM
10:32:32.961482 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameXmlySearchSM
10:32:32.961492 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameXmlyMineSM
10:32:32.961515 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQMultiDeviceTransformPlaySM
10:32:32.961526 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQMusicSM
10:32:32.961537 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQSearchSM
10:32:32.961547 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQRankSM
10:32:32.961557 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQFolderSM
10:32:32.961566 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQCacheListSM
10:32:32.961577 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQSingerSM
10:32:32.961587 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQMusicVoiceSM
10:32:32.961597 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQAiDjSM
10:32:32.961607 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQReportSM
10:32:32.961627 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQAlbumSM
10:32:32.961641 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQSectionSM
10:32:32.961662 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameQQRecommendSM
10:32:32.961675 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWyMusicRecommendSM
10:32:32.961687 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWyHighQualitySM
10:32:32.961697 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWyMusicSearchSM
10:32:32.961722 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWyMultiDeviceTransformPlaySM
10:32:32.961734 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWyMusicSingerSM
10:32:32.961745 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWyMusicUserSM
10:32:32.961756 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWySoundEffectSM
10:32:32.961789 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWyAiDjSM
10:32:32.961802 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameWyMusicVoiceSM
10:32:32.961813 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameYtCacheListSM
10:32:32.961920 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameYtMusicSM
10:32:32.961933 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameYtMusicPlayerSM
10:32:32.961943 11844 11844 I Router  : inject---className:YtLoginSM mMapping classNameYtLoginSM
10:32:32.962025 11844 11844 I Router  : inject---className:YtPurchaseSM
10:32:32.962042 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameAbsMediaCommonApi
10:32:32.962052 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameBtMusicVoiceSM
10:32:32.962061 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameBtMusicSM
10:32:32.962078 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameUsbMusicVoiceSM
10:32:32.962088 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameUsbMusicSM
10:32:32.962097 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameUsbMediaControllerSM
10:32:32.962106 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameUsbSearchSM
10:32:32.962115 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameXmlyRecommendDataSM
10:32:32.962125 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameXmlyMusicVoiceSM
10:32:32.962134 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameXmlyClassifySM
10:32:32.962143 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameXmlySearchSM
10:32:32.962154 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameXmlyMineSM
10:32:32.962164 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQMultiDeviceTransformPlaySM
10:32:32.962176 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQMusicSM
10:32:32.962185 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQSearchSM
10:32:32.962195 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQRankSM
10:32:32.962205 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQFolderSM
10:32:32.962214 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQCacheListSM
10:32:32.962224 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQSingerSM
10:32:32.962233 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQMusicVoiceSM
10:32:32.962242 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQAiDjSM
10:32:32.962290 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQReportSM
10:32:32.962302 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQAlbumSM
10:32:32.962312 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQSectionSM
10:32:32.962329 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameQQRecommendSM
10:32:32.962339 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWyMusicRecommendSM
10:32:32.962349 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWyHighQualitySM
10:32:32.962359 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWyMusicSearchSM
10:32:32.962369 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWyMultiDeviceTransformPlaySM
10:32:32.962379 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWyMusicSingerSM
10:32:32.962389 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWyMusicUserSM
10:32:32.962398 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWySoundEffectSM
10:32:32.962408 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWyAiDjSM
10:32:32.962423 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameWyMusicVoiceSM
10:32:32.962435 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtCacheListSM
10:32:32.962446 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtMusicSM
10:32:32.962455 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtMusicPlayerSM
10:32:32.962465 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtLoginSM
10:32:32.962476 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtRecommendSM
10:32:32.962486 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtMusicVoiceSM
10:32:32.962496 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtSubscribeSM
10:32:32.962506 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtSearchSM
10:32:32.962517 11844 11844 I Router  : inject---className:YtPurchaseSM mMapping classNameYtPurchaseSM
10:32:32.962595 11844 11844 I Router  : inject---className:YtSubscribeSM
10:32:32.962610 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameAbsMediaCommonApi
10:32:32.962620 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameBtMusicVoiceSM
10:32:32.962630 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameBtMusicSM
10:32:32.962640 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameUsbMusicVoiceSM
10:32:32.962650 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameUsbMusicSM
10:32:32.962659 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameUsbMediaControllerSM
10:32:32.962669 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameUsbSearchSM
10:32:32.962678 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameXmlyRecommendDataSM
10:32:32.962688 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameXmlyMusicVoiceSM
10:32:32.962701 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameXmlyClassifySM
10:32:32.962712 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameXmlySearchSM
10:32:32.962722 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameXmlyMineSM
10:32:32.962732 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQMultiDeviceTransformPlaySM
10:32:32.962741 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQMusicSM
10:32:32.962750 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQSearchSM
10:32:32.962760 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQRankSM
10:32:32.962769 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQFolderSM
10:32:32.962779 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQCacheListSM
10:32:32.962789 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQSingerSM
10:32:32.962801 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQMusicVoiceSM
10:32:32.962810 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQAiDjSM
10:32:32.962819 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQReportSM
10:32:32.962829 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQAlbumSM
10:32:32.962838 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQSectionSM
10:32:32.962847 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameQQRecommendSM
10:32:32.962857 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWyMusicRecommendSM
10:32:32.962867 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWyHighQualitySM
10:32:32.962877 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWyMusicSearchSM
10:32:32.962887 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWyMultiDeviceTransformPlaySM
10:32:32.962900 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWyMusicSingerSM
10:32:32.962910 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWyMusicUserSM
10:32:32.962920 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWySoundEffectSM
10:32:32.962929 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWyAiDjSM
10:32:32.962939 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameWyMusicVoiceSM
10:32:32.962948 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameYtCacheListSM
10:32:32.962958 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameYtMusicSM
10:32:32.962968 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameYtMusicPlayerSM
10:32:32.962979 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameYtLoginSM
10:32:32.962989 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameYtRecommendSM
10:32:32.963003 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameYtMusicVoiceSM
10:32:32.963013 11844 11844 I Router  : inject---className:YtSubscribeSM mMapping classNameYtSubscribeSM
10:32:32.963075 11844 11844 I Router  : inject---className:YtSearchSM
10:32:32.963089 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameAbsMediaCommonApi
10:32:32.963099 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameBtMusicVoiceSM
10:32:32.963109 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameBtMusicSM
10:32:32.963118 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameUsbMusicVoiceSM
10:32:32.963127 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameUsbMusicSM
10:32:32.963137 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameUsbMediaControllerSM
10:32:32.963151 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameUsbSearchSM
10:32:32.963160 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameXmlyRecommendDataSM
10:32:32.963170 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameXmlyMusicVoiceSM
10:32:32.963179 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameXmlyClassifySM
10:32:32.963189 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameXmlySearchSM
10:32:32.963198 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameXmlyMineSM
10:32:32.963208 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQMultiDeviceTransformPlaySM
10:32:32.963217 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQMusicSM
10:32:32.963236 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQSearchSM
10:32:32.963246 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQRankSM
10:32:32.963258 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQFolderSM
10:32:32.963268 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQCacheListSM
10:32:32.963277 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQSingerSM
10:32:32.963286 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQMusicVoiceSM
10:32:32.963295 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQAiDjSM
10:32:32.963305 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQReportSM
10:32:32.963314 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQAlbumSM
10:32:32.963324 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQSectionSM
10:32:32.963334 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameQQRecommendSM
10:32:32.963344 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWyMusicRecommendSM
10:32:32.963354 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWyHighQualitySM
10:32:32.963365 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWyMusicSearchSM
10:32:32.963375 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWyMultiDeviceTransformPlaySM
10:32:32.963386 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWyMusicSingerSM
10:32:32.963395 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWyMusicUserSM
10:32:32.963405 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWySoundEffectSM
10:32:32.963414 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWyAiDjSM
10:32:32.963424 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameWyMusicVoiceSM
10:32:32.963434 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameYtCacheListSM
10:32:32.963443 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameYtMusicSM
10:32:32.963452 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameYtMusicPlayerSM
10:32:32.963465 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameYtLoginSM
10:32:32.963705 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameYtRecommendSM
10:32:32.963723 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameYtMusicVoiceSM
10:32:32.963732 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameYtSubscribeSM
10:32:32.963741 11844 11844 I Router  : inject---className:YtSearchSM mMapping classNameYtSearchSM
10:32:32.963836 11844 11844 I Router  : inject---className:YtRecommendSM
10:32:32.963850 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameAbsMediaCommonApi
10:32:32.963861 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameBtMusicVoiceSM
10:32:32.963871 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameBtMusicSM
10:32:32.963888 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameUsbMusicVoiceSM
10:32:32.963898 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameUsbMusicSM
10:32:32.963907 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameUsbMediaControllerSM
10:32:32.963917 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameUsbSearchSM
10:32:32.963926 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameXmlyRecommendDataSM
10:32:32.963936 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameXmlyMusicVoiceSM
10:32:32.963945 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameXmlyClassifySM
10:32:32.963954 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameXmlySearchSM
10:32:32.963963 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameXmlyMineSM
10:32:32.963975 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQMultiDeviceTransformPlaySM
10:32:32.963985 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQMusicSM
10:32:32.963994 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQSearchSM
10:32:32.964003 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQRankSM
10:32:32.964021 11844 11844 I liblog  : 1
10:32:32.964021 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQCacheListSM
10:32:32.967559 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQSingerSM
10:32:32.967571 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQMusicVoiceSM
10:32:32.967581 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQAiDjSM
10:32:32.967592 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQReportSM
10:32:32.967602 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQAlbumSM
10:32:32.967619 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQSectionSM
10:32:32.967630 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameQQRecommendSM
10:32:32.967641 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWyMusicRecommendSM
10:32:32.967651 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWyHighQualitySM
10:32:32.967661 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWyMusicSearchSM
10:32:32.967672 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWyMultiDeviceTransformPlaySM
10:32:32.967682 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWyMusicSingerSM
10:32:32.967692 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWyMusicUserSM
10:32:32.967703 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWySoundEffectSM
10:32:32.967713 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWyAiDjSM
10:32:32.967727 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameWyMusicVoiceSM
10:32:32.967738 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameYtCacheListSM
10:32:32.967749 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameYtMusicSM
10:32:32.967759 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameYtMusicPlayerSM
10:32:32.967769 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameYtLoginSM
10:32:32.967780 11844 11844 I Router  : inject---className:YtRecommendSM mMapping classNameYtRecommendSM
10:32:33.020908 11844 12084 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicService user0] yunTing openSDK initSdk
10:32:33.025037 11844 12084 W MegaCar : [ (MegaCar.java:390)#determineEventHandler ]Handles events in main thread !
10:32:33.068813 11844 11844 I ServiceModelService0:  initService userHandles.size() > 2 ... 
10:32:33.069018 11844 11844 I ServiceModelService0: serviceType = serviceIpcSDK
10:32:33.069216 11844 11844 I ServiceStub: observer 
10:32:33.087705 11844 12084 I initSDK:deviceId=: 10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504
10:32:33.098932 11844 12134 I aaccdd1 : bindService result= true
10:32:33.126943 11844 11844 D MegaCarPropHelper: [ (MegaCarPropHelper.java:47)#lambda$new$0$mega-car-MegaCarPropHelper ]CarService connected
10:32:33.127049 11844 11844 I aaccdd1 : onServiceConnected service = com.voyah.media.service_mediamanager.service.MediaManagerService, userId = 0
10:32:33.127693 11844 11844 I ClientHeartbeat: Heartbeat check started
10:32:33.157129 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  recoverSourceImpl
10:32:33.157231 11844 11889 I YtPlayInfoSpUtil: getPlayInfo  : {"albumId":"1100002156582","enableLoadMore":true,"mediaId":"1015809379411","nextPage":1,"page":1,"prePage":1,"progress":0,"sort":true,"ytPlaySource":"PLAY_AUDIO"}
10:32:33.158445 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  recoverSourceImpl , mYtPlayInfo is : YtPlayInfo{mediaId='1015809379411', albumId='1100002156582', progress=0, mediaList=null, ytPlaySource=PLAY_AUDIO, page=1, prePage=1, nextPage=1, sort=true, enableLoadMore=true, keyWord='null'}
10:32:33.179687 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA page_size" took 0.000 ms
10:32:33.179721 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:33.179944 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA journal_mode" took 0.000 ms
10:32:33.179972 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA journal_mode=TRUNCATE" took 0.000 ms
10:32:33.180019 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA synchronous" took 0.000 ms
10:32:33.182685 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:33.182719 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:33.182744 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:33.182765 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:33.182877 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:33.183015 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 1.000 ms
10:32:33.183165 11844 12084 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/kradioADReport.db: "PRAGMA user_version;" took 0.000 ms
10:32:33.199053 11844 12084 W Settings: Setting android_id has moved from android.provider.Settings.System to android.provider.Settings.Secure, returning read-only value.
10:32:33.212682 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA page_size" took 0.000 ms
10:32:33.212706 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:33.213173 11844 12233 V SQLiteLog: (283) recovered 78 frames from WAL file /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db-wal
10:32:33.213293 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_mode" took 0.000 ms
10:32:33.213367 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA synchronous" took 0.000 ms
10:32:33.213385 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:33.213401 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:33.213416 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:33.213430 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:33.213489 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:33.213540 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:33.213719 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA page_size" took 0.000 ms
10:32:33.213737 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:33.213833 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_mode" took 0.000 ms
10:32:33.213879 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA synchronous" took 0.000 ms
10:32:33.213896 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:33.213910 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:33.213925 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:33.213938 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:33.213974 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:33.214020 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:33.214041 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA user_version;" took 0.000 ms
10:32:33.214219 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA user_version;" took 0.000 ms
10:32:33.214256 11844 12255 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:33.214290 11844 12255 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:33.214307 11844 12255 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:33.214334 11844 12255 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:33.214498 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "SELECT 1 FROM sqlite_master WHERE type = 'table' AND name='room_master_table'" took 0.000 ms
10:32:33.214517 11844 12233 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:33.214608 11844 12255 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:33.214627 11844 12255 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:33.214639 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "SELECT identity_hash FROM room_master_table WHERE id = 42 LIMIT 1" took 0.000 ms
10:32:33.214654 11844 12233 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:33.214671 11844 12255 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=d206c55433f6cf74a257d5bb0aae6219, channel=com.voyah.cockpit.voyahmusic, deviceid=null, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=null, lat=}
10:32:33.214717 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA temp_store = MEMORY;" took 0.000 ms
10:32:33.214741 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA recursive_triggers='ON';" took 0.000 ms
10:32:33.214939 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "CREATE TEMP TABLE room_table_modification_log (table_id INTEGER PRIMARY KEY, invalidated INTEGER NOT NULL DEFAULT 0)" took 0.000 ms
10:32:33.215323 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA page_size" took 0.000 ms
10:32:33.215341 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:33.215450 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_mode" took 0.000 ms
10:32:33.215503 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA synchronous" took 0.000 ms
10:32:33.215520 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:33.215534 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:33.215549 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:33.215563 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:33.215599 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:33.215640 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:33.215810 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "SELECT * FROM yt_mediainfo_list" took 0.000 ms
10:32:33.215830 11844 12233 D SQLiteCursor: received count(*) from native_fill_window: 20
10:32:33.216150 11844 12233 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  recoverSourceImpl ytMediaInfoLists size is :20
10:32:33.222865 11844 12084 W Settings: Setting android_id has moved from android.provider.Settings.System to android.provider.Settings.Secure, returning read-only value.
10:32:33.223964 11844 12084 D K-radio : │ 音乐
10:32:33.229525 11844 12255 D TrafficStats: tagSocket(85) with statsTag=0xffffffff, statsUid=-1
10:32:33.235939 11844 12084 W Settings: Setting android_id has moved from android.provider.Settings.System to android.provider.Settings.Secure, returning read-only value.
10:32:33.241580 11844 12084 W Settings: Setting android_id has moved from android.provider.Settings.System to android.provider.Settings.Secure, returning read-only value.
10:32:33.241730 11844 12084 I player_log_tag: PlayerManager.init->
10:32:33.413626 11844 12084 V Location: 定位方式GPS
10:32:33.427240 11844 12084 V location: GPS定位失败，切换为网络定位
10:32:33.427434 11844 12084 V Location: ----------inLocation() 添加监听----------
10:32:33.433750 11844 12084 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicService user0] 初始化、激活成功=ie70682025011610029074
10:32:33.434086 11844 12387 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:33.434107 11844 12387 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:33.434119 11844 12387 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:33.434132 11844 12084 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  recoverSourceImpl
10:32:33.434185 11844 12387 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:33.434262 11844 12084 I YtPlayInfoSpUtil: getPlayInfo  : {"albumId":"1100002156582","enableLoadMore":true,"mediaId":"1015809379411","nextPage":1,"page":1,"prePage":1,"progress":0,"sort":true,"ytPlaySource":"PLAY_AUDIO"}
10:32:33.434315 11844 12387 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:33.434330 11844 12387 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:33.434357 11844 12387 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:33.434983 11844 12084 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  recoverSourceImpl , mYtPlayInfo is : YtPlayInfo{mediaId='1015809379411', albumId='1100002156582', progress=0, mediaList=null, ytPlaySource=PLAY_AUDIO, page=1, prePage=1, nextPage=1, sort=true, enableLoadMore=true, keyWord='null'}
10:32:33.435031 11844 12387 D TrafficStats: tagSocket(90) with statsTag=0xffffffff, statsUid=-1
10:32:33.446266 11844 12084 W MessageQueue: Handler (com.kaolafm.opensdk.OpenSDK$1) {fecca7c} sending message to a Handler on a dead thread
10:32:33.446266 11844 12084 W MessageQueue: java.lang.IllegalStateException: Handler (com.kaolafm.opensdk.OpenSDK$1) {fecca7c} sending message to a Handler on a dead thread
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.MessageQueue.enqueueMessage(MessageQueue.java:560)
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.Handler.enqueueMessage(Handler.java:778)
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.Handler.sendMessageAtTime(Handler.java:727)
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.Handler.sendMessageDelayed(Handler.java:697)
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.Handler.sendEmptyMessageDelayed(Handler.java:662)
10:32:33.446266 11844 12084 W MessageQueue: 	at com.kaolafm.opensdk.OpenSDK$1.handleMessage(OpenSDK.java:64)
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.Handler.dispatchMessage(Handler.java:106)
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.Looper.loopOnce(Looper.java:201)
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.Looper.loop(Looper.java:288)
10:32:33.446266 11844 12084 W MessageQueue: 	at android.os.HandlerThread.run(HandlerThread.java:67)
10:32:33.451126 11844 12390 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:33.451145 11844 12390 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:33.451155 11844 12390 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:33.451165 11844 12390 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:33.451254 11844 12390 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:33.451262 11844 12390 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:33.451279 11844 12390 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:33.451846 11844 12390 D TrafficStats: tagSocket(73) with statsTag=0xffffffff, statsUid=-1
10:32:33.501117 11844 12255 D okhttp.Http2: >> CONNECTION 505249202a20485454502f322e300d0a0d0a534d0d0a0d0a
10:32:33.501354 11844 12255 D okhttp.Http2: >> 0x00000000     6 SETTINGS      
10:32:33.501478 11844 12255 D okhttp.Http2: >> 0x00000000     4 WINDOW_UPDATE 
10:32:33.501691 11844 12255 D okhttp.TaskRunner: Q10005 scheduled after   0 µs: OkHttp iovopen.radio.cn
10:32:33.501974 11844 12255 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:33.503859 11844 12255 D okhttp.Http2: >> 0x00000003   302 HEADERS       END_STREAM|END_HEADERS
10:32:33.526079 11844 12406 D okhttp.TaskRunner: Q10005 starting              : OkHttp iovopen.radio.cn
10:32:33.532152 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:33.532229 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:33.532261 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 240 µs: OkHttp ConnectionPool
10:32:33.535009 11844 12406 D okhttp.Http2: << 0x00000000    18 SETTINGS      
10:32:33.535158 11844 12406 D okhttp.TaskRunner: Q10002 scheduled after   0 µs: OkHttp iovopen.radio.cn applyAndAckSettings
10:32:33.535257 11844 12406 D okhttp.Http2: << 0x00000000     4 WINDOW_UPDATE 
10:32:33.535353 11844 12405 D okhttp.TaskRunner: Q10002 starting              : OkHttp iovopen.radio.cn applyAndAckSettings
10:32:33.535469 11844 12405 D okhttp.TaskRunner: Q10004 scheduled after   0 µs: OkHttp iovopen.radio.cn onSettings
10:32:33.535568 11844 12405 D okhttp.Http2: >> 0x00000000     0 SETTINGS      ACK
10:32:33.535754 11844 12405 D okhttp.TaskRunner: Q10002 finished run in 403 µs: OkHttp iovopen.radio.cn applyAndAckSettings
10:32:33.546987 11844 12405 D okhttp.TaskRunner: Q10004 starting              : OkHttp iovopen.radio.cn onSettings
10:32:33.547029 11844 12405 D okhttp.TaskRunner: Q10004 finished run in  58 µs: OkHttp iovopen.radio.cn onSettings
10:32:33.554102 11844 12387 D okhttp.Http2: >> CONNECTION 505249202a20485454502f322e300d0a0d0a534d0d0a0d0a
10:32:33.554271 11844 12387 D okhttp.Http2: >> 0x00000000     6 SETTINGS      
10:32:33.554369 11844 12387 D okhttp.Http2: >> 0x00000000     4 WINDOW_UPDATE 
10:32:33.554479 11844 12387 D okhttp.TaskRunner: Q10009 scheduled after   0 µs: OkHttp iovopen.radio.cn
10:32:33.555578 11844 12405 D okhttp.TaskRunner: Q10009 starting              : OkHttp iovopen.radio.cn
10:32:33.555750 11844 12405 D okhttp.Http2: >> 0x00000000     8 GOAWAY        
10:32:33.555853 11844 12405 D okhttp.TaskRunner: Q10009 finished run in 283 µs: OkHttp iovopen.radio.cn
10:32:33.556287 11844 12387 D okhttp.Http2: >> 0x00000005   371 HEADERS       END_STREAM|END_HEADERS
10:32:33.584170 11844 12406 D okhttp.Http2: << 0x00000000     0 SETTINGS      ACK
10:32:33.596495 11844 12406 D okhttp.Http2: << 0x00000003   220 HEADERS       END_HEADERS
10:32:33.597198 11844 12406 D okhttp.Http2: << 0x00000003   110 DATA          
10:32:33.597368 11844 12406 D okhttp.Http2: << 0x00000003     0 DATA          END_STREAM
10:32:33.610155 11844 12390 D okhttp.Http2: >> CONNECTION 505249202a20485454502f322e300d0a0d0a534d0d0a0d0a
10:32:33.610316 11844 12390 D okhttp.Http2: >> 0x00000000     6 SETTINGS      
10:32:33.610432 11844 12390 D okhttp.Http2: >> 0x00000000     4 WINDOW_UPDATE 
10:32:33.610551 11844 12390 D okhttp.TaskRunner: Q10013 scheduled after   0 µs: OkHttp iovopen.radio.cn
10:32:33.610696 11844 12405 D okhttp.TaskRunner: Q10013 starting              : OkHttp iovopen.radio.cn
10:32:33.611650 11844 12405 D okhttp.Http2: >> 0x00000000     8 GOAWAY        
10:32:33.612450 11844 12405 D okhttp.TaskRunner: Q10013 finished run in   2 ms: OkHttp iovopen.radio.cn
10:32:33.613688 11844 12390 D okhttp.Http2: >> 0x00000007   391 HEADERS       END_STREAM|END_HEADERS
10:32:33.622000 11844 12406 D okhttp.Http2: << 0x00000005   193 HEADERS       END_HEADERS
10:32:33.622821 11844 12406 D okhttp.Http2: << 0x00000005    84 DATA          
10:32:33.623000 11844 12406 D okhttp.Http2: << 0x00000005     0 DATA          END_STREAM
10:32:33.679358 11844 12406 D okhttp.Http2: << 0x00000007   220 HEADERS       END_HEADERS
10:32:33.680230 11844 12406 D okhttp.Http2: << 0x00000007   360 DATA          
10:32:33.680918 11844 12406 D okhttp.Http2: << 0x00000007     0 DATA          END_STREAM
10:32:33.683707 11844 12390 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:33.687336 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:33.687434 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:33.687500 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 185 µs: OkHttp ConnectionPool
10:32:33.698655 11844 11844 D K-radio : │ 网络请求出现错误, ApiException{code=40000, message='参数有误', haveShow=false}, cause=null
10:32:33.701943 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA page_size" took 0.000 ms
10:32:33.703237 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:33.703680 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA journal_mode" took 0.000 ms
10:32:33.703702 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA journal_mode=TRUNCATE" took 0.000 ms
10:32:33.703726 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA synchronous" took 0.000 ms
10:32:33.703748 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:33.703763 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:33.703779 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:33.703793 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:33.703859 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:33.703924 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:33.704001 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "PRAGMA user_version;" took 0.000 ms
10:32:33.707633 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA page_size" took 0.000 ms
10:32:33.707657 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:33.707778 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA journal_mode" took 0.000 ms
10:32:33.707797 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA journal_mode=TRUNCATE" took 0.000 ms
10:32:33.707817 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA synchronous" took 0.000 ms
10:32:33.707839 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:33.707855 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:33.707872 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:33.707886 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:33.707942 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:33.708005 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 1.000 ms
10:32:33.708241 11844 11844 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "PRAGMA user_version;" took 0.000 ms
10:32:33.762565 11844 12390 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "SELECT T."_id",T."TYPE",T."JSON" FROM "CONFIG_DATA" T" took 0.000 ms
10:32:33.762600 11844 12390 D SQLiteCursor: received count(*) from native_fill_window: 2
10:32:33.765686 11844 12255 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:32:33.765866 11844 12255 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "INSERT OR REPLACE INTO "CONFIG_DATA" ("_id","TYPE","JSON") VALUES (?,?,?)" took 0.000 ms
10:32:33.773481 11844 12387 D J4A     : J4ALoader: OK: 'android.os.Build$VERSION' loaded
10:32:33.773511 11844 12387 D J4A     : J4ALoader: OK: 'android.os.Build' loaded
10:32:33.773529 11844 12387 D J4A     : J4ALoader: OK: 'java.nio.Buffer' loaded
10:32:33.773542 11844 12387 D J4A     : J4ALoader: OK: 'java.nio.ByteBuffer' loaded
10:32:33.773564 11844 12387 D J4A     : J4ALoader: OK: 'java.util.ArrayList' loaded
10:32:33.773571 11844 12387 I J4A     : API-Level: 33
10:32:33.773616 11844 12387 D J4A     : J4ALoader: OK: 'android.media.AudioAttributes$Builder' loaded
10:32:33.773632 11844 12387 D J4A     : J4ALoader: OK: 'android.media.AudioAttributes' loaded
10:32:33.773867 11844 12387 D J4A     : J4ALoader: OK: 'android.media.AudioFormat$Builder' loaded
10:32:33.773876 11844 12387 D J4A     : J4ALoader: OK: 'android.media.AudioFormat' loaded
10:32:33.774062 11844 12387 D J4A     : J4ALoader: OK: 'android.media.AudioTrack$Builder' loaded
10:32:33.774086 11844 12387 D J4A     : J4ALoader: OK: 'android.media.AudioTrack' loaded
10:32:33.774118 11844 12387 D J4A     : J4ALoader: OK: 'android.media.MediaCodec$BufferInfo' loaded
10:32:33.774464 11844 12387 D J4A     : J4ALoader: OK: 'android.media.MediaCodec' loaded
10:32:33.774484 11844 12387 D J4A     : J4ALoader: OK: 'android.media.MediaFormat' loaded
10:32:33.774513 11844 12387 D J4A     : J4ALoader: OK: 'android.media.PlaybackParams' loaded
10:32:33.774551 11844 12387 D J4A     : J4ALoader: OK: 'android.os.Bundle' loaded
10:32:33.774595 11844 12387 D J4A     : J4ALoader: OK: 'tv.danmaku.ijk.media.player.misc.IMediaDataSource' loaded
10:32:33.774612 11844 12387 D J4A     : J4ALoader: OK: 'tv.danmaku.ijk.media.player.misc.IAndroidIO' loaded
10:32:33.774626 11844 12387 D J4A     : J4ALoader: OK: 'tv.danmaku.ijk.media.player.IjkMediaPlayer' loaded
10:32:33.775943 11844 12387 D IJKMEDIA: FUNC >> JNI_OnLoad() 
10:32:33.776013 11844 12387 I IJKMEDIA: ijk_version_info: k0.8.8-226-g5bb1d3d
10:32:33.793489 11844 12387 I IJKMEDIA: av_version_info: ff3.4--ijk0.8.8--20230901--001
10:32:33.793543 11844 12387 I IJKMEDIA: ===== custom modules begin =====
10:32:33.793570 11844 12387 I IJKMEDIA: register demuxer : ijklivehook
10:32:33.793598 11844 12387 I IJKMEDIA: ===== custom modules end =====
10:32:33.794678 11844 12387 I IJKMEDIA: ffp_InitPlayerQueue
10:32:33.794701 11844 12387 I IJKMEDIA: SDL_CreateThreadEx: ff_destroy
10:32:33.794757 11844 12387 I IJKMEDIA: SDL_CreateThreadEx: ff_destroy over
10:32:33.794817 11844 12387 D IJKMEDIA: FUNC << JNI_OnLoad() 
10:32:33.795371 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_native_init() 
10:32:33.795410 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_native_init() 
10:32:33.795424 11844 11844 I IJKMEDIA: FUNC >> IjkMediaPlayer_native_setup() 
10:32:33.795434 11844 11844 I IJKMEDIA: ijkmp_android_create() weak_this[0x7fc633692c]
10:32:33.795446 11844 11844 I IJKMEDIA: ffp_create ffp_create FFPlayer[0xb400007bdc857100]
10:32:33.795460 11844 11844 D IJKMEDIA: loudness_context_create()
10:32:33.795605 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref++: 1 
10:32:33.795619 11844 11844 D IJKMEDIA: ffpipeline_create_from_android()
10:32:33.795632 11844 11844 I IJKMEDIA: ijkmp_set_weak_thiz , mp[0xb400007b6c8a4230],weak_thiz[0x2d9a]
10:32:33.795640 11844 11844 D IJKMEDIA: ijkmp_set_inject_opaque(0x2d9a)
10:32:33.795650 11844 11844 D IJKMEDIA: ijkmp_set_inject_opaque()=void
10:32:33.795657 11844 11844 D IJKMEDIA: ijkmp_set_ijkio_inject_opaque(0x2d9a)
10:32:33.795798 11844 11844 D IJKMEDIA: ijkmp_set_ijkio_inject_opaque()=void
10:32:33.795807 11844 11844 D IJKMEDIA: ijkmp_android_set_mediacodec_select_callback()
10:32:33.795813 11844 11844 D IJKMEDIA: ffpipeline_set_mediacodec_select_callback
10:32:33.795819 11844 11844 D IJKMEDIA: ijkmp_android_set_mediacodec_select_callback()=void
10:32:33.795825 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_native_setup() 
10:32:33.795866 11844 11844 I player_log_tag: message = 666666, arg1 = 0, arg2 = 0
10:32:33.796327 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicService user0] onPlayerInitComplete is :true
10:32:33.860426 11844 12255 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "COMMIT;" took 12.000 ms
10:32:33.860640 11844 12255 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "SELECT COUNT(*) FROM "CONFIG_DATA"" took 0.000 ms
10:32:33.869128 11844 12390 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:33.869169 11844 12390 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:33.869181 11844 12390 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:33.869194 11844 12390 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:33.869298 11844 12390 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:33.869322 11844 12390 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:33.869345 11844 12390 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:33.870488 11844 12390 D okhttp.Http2: >> 0x00000009   399 HEADERS       END_STREAM|END_HEADERS
10:32:33.910270 11844 12550 D IJKMEDIA: afade - timer thread: begin 
10:32:33.916397 11844 12549 I IJKMEDIA: SDL_RunThread: [12549] ff_destroy
10:32:33.916428 11844 12549 I IJKMEDIA: destroyCallback destroyThread wait
10:32:34.030833 11844 12406 D okhttp.Http2: << 0x00000009   220 HEADERS       END_HEADERS
10:32:34.032529 11844 12406 D okhttp.Http2: << 0x00000009  1310 DATA          
10:32:34.032658 11844 12406 D okhttp.Http2: << 0x00000009     0 DATA          END_STREAM
10:32:34.077783 11844 12390 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:34.119278 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:34.119322 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:34.119352 11844 12405 D okhttp.TaskRunner: Q10001 finished run in  94 µs: OkHttp ConnectionPool
10:32:34.119597 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtRecommendSM user0] onResult getAiRadioList size is :9
10:32:34.514077 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:34.519295 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:34.519654 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:34.581800 11844 11889 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/getplaylist clientHost =media_manager code =0 token =e5b89352-4471-42b2-8bb5-1c89fb7ae385
10:32:34.846525 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:34.846582 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:34.921121 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:34.927257 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:34.927292 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:35.113566 11844 11889 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/getplaylist clientHost =media_manager code =0 token =b227920b-4e95-4513-9edf-bdba9ef3fc82
10:32:35.613805 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:35.622562 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:35.622624 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:35.697767 11844 11889 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/getplaylist clientHost =media_manager code =0 token =a534ea92-ee54-4946-bda0-671943f48965
10:32:35.726134 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:35.729079 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:35.729131 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:35.791249 11844 11889 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/getplaylist clientHost =media_manager code =0 token =3d3ea021-e770-4b03-a716-d25304d379c9
10:32:42.650889 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:42.650972 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:42.659702 11844 12262 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/getplaylist clientHost =media_manager code =0 token =cc0feeb6-65d5-432e-8f75-7dd21b4b9143
10:32:42.661244 11844 12262 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/GETLIKE clientHost =media_manager code =0 token =f742dc11-1e37-43db-8447-79d7250301f8
10:32:42.670831 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:42.707901 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:42.708775 11844 12262 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:42.714137 11844 11889 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/GETLIKE clientHost =media_manager code =0 token =2d86481e-b21e-4f1a-8c68-ef08c04b04f3
10:32:42.716140 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:42.733770 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  displayIdChangeImpl displayId =0
10:32:42.733863 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] onDisplayIdChanged changed: false, displayId: 0, mCurrentDisplayId: 0
10:32:42.767987 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:42.768134 11844 12262 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:42.806939 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:42.807022 11844 12262 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:42.836524 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:42.836606 11844 12262 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:42.839396 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:42.839444 11844 12262 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:42.847726 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:42.851441 11844 11889 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/GETLIKE clientHost =media_manager code =0 token =6c1aa495-5a6e-4ca8-a995-4b662b9e88a6
10:32:42.853314 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:42.853899 11844 11889 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/GETLIKE clientHost =media_manager code =0 token =0f05f24d-d040-4a6f-a3c7-be84a103778f
10:32:42.936656 11844 12387 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:42.936760 11844 12387 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:42.936773 11844 12387 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:42.936786 11844 12387 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:42.936883 11844 12387 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:42.936902 11844 12387 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:42.936922 11844 12387 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:42.938060 11844 12387 D okhttp.Http2: >> 0x0000000b   391 HEADERS       END_STREAM|END_HEADERS
10:32:42.996390 11844 12406 D okhttp.Http2: << 0x0000000b   222 HEADERS       END_HEADERS
10:32:42.996939 11844 12406 D okhttp.Http2: << 0x0000000b   756 DATA          
10:32:42.997091 11844 12406 D okhttp.Http2: << 0x0000000b     0 DATA          END_STREAM
10:32:43.000618 11844 12387 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:43.001047 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:43.001085 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:43.001113 11844 12405 D okhttp.TaskRunner: Q10001 finished run in  82 µs: OkHttp ConnectionPool
10:32:43.004913 11844 11844 I IPCServiceEngine: onResponse: url = yt_music/ytmusicsm/getcategorytree clientHost =ui userId =0 code =0 token =325b5516-218c-4901-a7fb-7902db74f56e
10:32:43.057498 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:43.057573 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:43.063588 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:43.063627 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:43.074232 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:43.074279 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:43.079747 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:43.079828 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:43.086345 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtRecommendSM user0] getRecommendAiRadio cache size is :9
10:32:43.086378 11844 11889 I IPCServiceEngine: onResponse: url = yt_music/ytrecommendsm/getrecommendairadio clientHost =ui userId =0 code =0 token =4149d77c-8b3e-498e-8d8b-91c960bef4f8
10:32:43.092192 11844 12255 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:43.092216 11844 12255 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:43.092227 11844 12255 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:43.092240 11844 12255 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:43.092758 11844 12255 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:43.092779 11844 12255 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:43.092826 11844 12255 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:43.094986 11844 12255 D okhttp.Http2: >> 0x0000000d   391 HEADERS       END_STREAM|END_HEADERS
10:32:43.130036 11844 12390 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:43.130054 11844 12390 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:43.130064 11844 12390 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:43.130074 11844 12390 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:43.130156 11844 12390 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:43.130184 11844 12390 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:43.130204 11844 12390 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:43.131270 11844 12390 D okhttp.Http2: >> 0x0000000f   385 HEADERS       END_STREAM|END_HEADERS
10:32:43.150202 11844 12406 D okhttp.Http2: << 0x0000000d   220 HEADERS       END_HEADERS
10:32:43.151134 11844 12406 D okhttp.Http2: << 0x0000000d   757 DATA          
10:32:43.151332 11844 12406 D okhttp.Http2: << 0x0000000d     0 DATA          END_STREAM
10:32:43.157312 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtRecommendSM user0] getRecommendBroadCast onSuccess ,size is :5
10:32:43.159036 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [LocationHelper user0] passive getLocation == null
10:32:43.159742 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [LocationHelper user0] fused getLocation == null
10:32:43.160777 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [LocationHelper user0] gps getLocation == null
10:32:43.161894 11844 12233 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtRecommendSM user0] getBroadCastList: request 10000764
10:32:43.163949 11844 12387 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:43.163986 11844 12387 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:43.164003 11844 12387 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:43.164018 11844 12387 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:43.164318 11844 12387 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:43.164377 11844 12387 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:43.164413 11844 12387 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:43.167159 11844 12387 D okhttp.Http2: >> 0x00000011   399 HEADERS       END_STREAM|END_HEADERS
10:32:43.191014 11844 12406 D okhttp.Http2: << 0x0000000f   222 HEADERS       END_HEADERS
10:32:43.191561 11844 12406 D okhttp.Http2: << 0x0000000f   540 DATA          
10:32:43.191750 11844 12406 D okhttp.Http2: << 0x0000000f     0 DATA          END_STREAM
10:32:43.192987 11844 11844 I IPCServiceEngine: onResponse: url = yt_music/ytmusicsm/getsubcategorylist clientHost =ui userId =0 code =0 token =93e47bea-09cf-4fc2-bc3f-463fc3f7f679
10:32:43.243963 11844 12406 D okhttp.Http2: << 0x00000011   220 HEADERS       END_HEADERS
10:32:43.244815 11844 12406 D okhttp.Http2: << 0x00000011  1547 DATA          
10:32:43.245075 11844 12406 D okhttp.Http2: << 0x00000011     0 DATA          END_STREAM
10:32:43.248225 11844 12387 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:43.249174 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:43.249233 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:43.249261 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 107 µs: OkHttp ConnectionPool
10:32:43.249499 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtRecommendSM user0] onResult getBroadCastList size is :20
10:32:43.249792 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtCacheListSM user0] updateCacheList type is : PLAY_BROADCAST_RECOMMEND_FRAGMENT, size is : 20
10:32:43.249825 11844 11844 I IPCServiceEngine: onResponse: url = yt_music/ytrecommendsm/getrecommendbroadcast clientHost =ui userId =0 code =0 token =e4a4256b-f8e1-4687-918e-cadef4ad1ac6
10:32:48.295045 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  playImpl info
10:32:48.295098 11844 12262 D AudioZoneSwitch: getCurZoneIdByUid uid 10040
10:32:48.303795 11844 12262 D AudioZoneSwitch: getZoneIdByDisplayId displayId 0
10:32:48.304368 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] setZoneId  lastZoneId 0  ,displayId 0  ,getZoneId 0
10:32:48.318397 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] request in mHasFocus = false,requestResult=1
10:32:48.318449 11844 12262 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/audioSuccess
10:32:48.318584 11844 12262 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:48.320366 11844 12262 I player_log_tag: AlbumPlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.321422 11844 12262 I player_log_tag: BroadcastPlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.321746 11844 12262 I player_log_tag: TVPlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.321941 11844 12262 I player_log_tag: RadioPlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.322111 11844 12262 I player_log_tag: LivePlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.322261 11844 12262 I player_log_tag: OneKeyListenPlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.322339 11844 12262 I player_log_tag: PurchaseOneKeyListenPlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.322475 11844 12262 I player_log_tag: FeaturePlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.322629 11844 12262 I player_log_tag: AlbumPlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.322702 11844 12262 I player_log_tag: PlayerManager.startNewBuilder->different type
10:32:48.322742 11844 12262 I player_log_tag: AlbumPlayListControl.BasePlayListControl构造器->修改mPosition = 0
10:32:48.322840 11844 12262 I player_log_tag: PlayerManager$3.startNewBuilder->get play list success
10:32:48.322882 11844 12262 I player_log_tag: AlbumPlayListControl.release->修改mPosition = 0
10:32:48.322926 11844 12262 I player_log_tag: PlayerManager->initBuilder: 
10:32:48.322974 11844 12262 I player_log_tag: PlayerManager.initBuilder->type = 1 id = 1015809379411
10:32:48.323044 11844 12262 I player_log_tag: AlbumPlayListControl.initPlayList->album
10:32:48.323081 11844 12262 I player_log_tag: AlbumPlayListControl.initPlayList->play audio, get audio detail
10:32:48.326278 11844 12255 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:48.326300 11844 12255 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:48.326314 11844 12255 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:48.326329 11844 12255 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:48.326434 11844 12255 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:48.326448 11844 12255 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:48.326473 11844 12255 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:48.326769 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:48.326804 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:48.327679 11844 12255 D okhttp.Http2: >> 0x00000013   376 HEADERS       END_STREAM|END_HEADERS
10:32:48.328269 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onplaystatechanged
10:32:48.336220 11844 11875 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] onFocusChanged=============zoneId= 0 ,changed= 1 ,usage=1
10:32:48.345373 11844 11889 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:48.345426 11844 11889 I YtPlayInfoSpUtil: getPlayItem  : {"alumName":"新闻纵横","audioId":"1100002156582","category":0,"cover":"https://iovimg.radio.cn/mz/images/202211/bedcc90c-d0f1-456e-8036-6f68008f8873/default.jpg","duration":128444,"endAudition":30000,"isAscOrDesc":true,"isAudition":false,"isBuyStatus":false,"isLike":false,"isLiving":false,"isPay":false,"isPlay":false,"isPlaying":false,"isVip":false,"mediaId":"1015809379411","mediaName":"泽连斯基称将和美方团队举行会谈 讨论俄乌和谈可能性与对乌安全保障问题","orderNumber":0,"playType":1,"quality":0,"singerName":"新闻纵横","sourceType":"yt_music","startAudition":0,"visible":true}
10:32:48.384468 11844 12406 D okhttp.Http2: << 0x00000013   222 HEADERS       END_HEADERS
10:32:48.387991 11844 12406 D okhttp.Http2: << 0x00000013   866 DATA          
10:32:48.392349 11844 12406 D okhttp.Http2: << 0x00000013     0 DATA          END_STREAM
10:32:48.397424 11844 12255 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:48.397674 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:48.397734 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:48.397781 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 115 µs: OkHttp ConnectionPool
10:32:48.398050 11844 11844 I player_log_tag: AlbumPlayListControl$6.getAudioInfo->success
10:32:48.398086 11844 11844 I player_log_tag: AlbumPlayListControl->getAlbumInfo
10:32:48.406372 11844 11872 W System  : A resource failed to call ContentProviderClient.close. 
10:32:48.406504 11844 11872 W System  : A resource failed to call ContentProviderClient.close. 
10:32:48.408825 11844 12390 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:48.408913 11844 12390 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:48.408941 11844 12390 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:48.409005 11844 12390 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:48.409143 11844 12390 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:48.409158 11844 12390 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:48.409183 11844 12390 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:48.410804 11844 12390 D okhttp.Http2: >> 0x00000015   376 HEADERS       END_STREAM|END_HEADERS
10:32:48.458472 11844 12406 D okhttp.Http2: << 0x00000015   222 HEADERS       END_HEADERS
10:32:48.459162 11844 12406 D okhttp.Http2: << 0x00000015   901 DATA          
10:32:48.459815 11844 12406 D okhttp.Http2: << 0x00000015     0 DATA          END_STREAM
10:32:48.462308 11844 12390 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:48.462660 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:48.462783 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:48.462822 11844 11844 I player_log_tag: AlbumPlayListControl$5.getAlbumInfo->success
10:32:48.462865 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 217 µs: OkHttp ConnectionPool
10:32:48.464506 11844 12387 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:48.464595 11844 12387 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:48.464646 11844 12387 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:48.464673 11844 12387 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:48.464781 11844 12387 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:48.464794 11844 12387 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:48.464818 11844 12387 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:48.466129 11844 12387 D okhttp.Http2: >> 0x00000017   414 HEADERS       END_STREAM|END_HEADERS
10:32:48.532723 11844 12406 D okhttp.Http2: << 0x00000017   220 HEADERS       END_HEADERS
10:32:48.533231 11844 12406 D okhttp.Http2: << 0x00000017  1067 DATA          
10:32:48.534236 11844 12406 D okhttp.Http2: << 0x00000017  1133 DATA          
10:32:48.534398 11844 12406 D okhttp.Http2: << 0x00000017     0 DATA          END_STREAM
10:32:48.535241 11844 12387 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:48.535559 11844 11844 I player_log_tag: AlbumPlayListControl$5$1.getAlbumInfo->get play list success
10:32:48.535841 11844 11844 I player_log_tag: AlbumPlayListControl.release->修改mPosition = 0
10:32:48.535898 11844 11844 I player_log_tag: AlbumPlayListControl.updatePlayListContent->从服务端返回的页码 4
10:32:48.535949 11844 11844 I player_log_tag: AlbumPlayListControl.updatePlayListContent->needPlayIndex = 8
10:32:48.535978 11844 11844 I player_log_tag: PlayerManager$3$1.startNewBuilder->get play list success
10:32:48.536001 11844 11844 I player_log_tag: AlbumPlayListControl.setCurPosition(O)->修改mPosition = 8
10:32:48.536023 11844 11844 I player_log_tag: AlbumPlayListControl.setCurPosition-> position = 8
10:32:48.536184 11844 11844 I player_log_tag: PlayControl.start->Play control start....: null
10:32:48.537374 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onplaylistchanged
10:32:48.537782 11844 12255 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:48.537804 11844 12255 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:48.537816 11844 12255 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:48.537829 11844 12255 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:48.537984 11844 12255 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:48.538004 11844 12255 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:48.538036 11844 12255 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:48.538143 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:48.538227 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:48.538300 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 176 µs: OkHttp ConnectionPool
10:32:48.539822 11844 12255 D okhttp.Http2: >> 0x00000019   388 HEADERS       END_STREAM|END_HEADERS
10:32:48.548914 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:48.549119 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "delete from yt_mediainfo_list" took 0.000 ms
10:32:48.551487 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "COMMIT;" took 2.000 ms
10:32:48.552136 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:48.552394 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552444 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552494 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552532 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552565 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552597 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552630 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552663 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552695 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552740 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552787 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552857 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552890 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552919 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.552973 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.553018 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.553051 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.553097 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.553267 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.553306 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "INSERT OR REPLACE INTO `yt_mediainfo_list` (`id`,`media_id`,`media_name`,`album_id`,`album_name`,`album_cover`,`category`,`duration`,`progress`,`is_vip`,`is_pay`,`is_audition`,`is_buy_status`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:32:48.554384 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "COMMIT;" took 1.000 ms
10:32:48.554659 11844 13638 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:48.555262 11844 13638 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "SELECT * FROM room_table_modification_log WHERE invalidated = 1;" took 0.000 ms
10:32:48.555315 11844 13638 D SQLiteCursor: received count(*) from native_fill_window: 0
10:32:48.555412 11844 13638 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "COMMIT;" took 0.000 ms
10:32:48.559191 11844 13639 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:48.559324 11844 13639 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "SELECT * FROM room_table_modification_log WHERE invalidated = 1;" took 0.000 ms
10:32:48.559349 11844 13639 D SQLiteCursor: received count(*) from native_fill_window: 0
10:32:48.559433 11844 13639 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_mediainfo_list.db: "COMMIT;" took 0.000 ms
10:32:48.609325 11844 12406 D okhttp.Http2: << 0x00000019   220 HEADERS       END_HEADERS
10:32:48.609982 11844 12406 D okhttp.Http2: << 0x00000019   351 DATA          
10:32:48.610703 11844 12406 D okhttp.Http2: << 0x00000019     0 DATA          END_STREAM
10:32:48.612886 11844 12255 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:48.613014 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:48.613099 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:48.613148 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 133 µs: OkHttp ConnectionPool
10:32:48.613275 11844 11844 I BasePlayControl: setPlayUrl 开始选择播放地址
10:32:48.613337 11844 11844 I player_log_tag: AlbumPlayControl->soundQuality=  null
10:32:48.613355 11844 11844 I BasePlayControl: setPlayUrl 未设置音质，将按照对应默认格式选择播放地址
10:32:48.613371 11844 11844 I BasePlayControl: setPlayUrl 查询播放资源默认格式：mp3
10:32:48.613432 11844 11844 I BasePlayControl: setPlayUrl 找到对应资源格式的播放地址：旧版码率为32 ,新版码率为32
10:32:48.613454 11844 11844 I BasePlayControl: setPlayUrl 确定最终码率---->旧版码率为32 ,新版码率为32
10:32:48.613469 11844 11844 I BasePlayControl: getPlayurl:https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3
10:32:48.613524 11844 11844 I player_log_tag: play url= https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3
10:32:48.613615 11844 11844 I player_log_tag: PlayerCustomizeManager.disposePlay->isDispose = false
10:32:48.613668 11844 11844 I player_log_tag: PlayerService.checkAudioFocus->isLoseAudioFocus = true
10:32:48.613716 11844 11844 I player_log_tag: PlayerService.checkAudioFocus->no need
10:32:48.613759 11844 11844 I player_log_tag: PlayerCustomizeManager.getStreamChannel->streamTypeChannel:3
10:32:48.613793 11844 11844 I player_log_tag: PlayerService.startInner->sync start executing.
10:32:48.613900 11844 11844 I player_log_tag: IjkMediaPlayer.stop->stop
10:32:48.613941 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_stop() 
10:32:48.613954 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref++: 2 
10:32:48.613964 11844 11844 D IJKMEDIA: ijkmp_stop_and_wait()
10:32:48.613973 11844 11844 D IJKMEDIA: ijkmp_stop_l()
10:32:48.613988 11844 11844 D IJKMEDIA: ijkmp_stop_and_wait()=-3
10:32:48.613995 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref--: 1 
10:32:48.614014 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_stop() 
10:32:48.614042 11844 11844 I player_log_tag: IjkMediaPlayer.reset->reset 
10:32:48.614069 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_stop() 
10:32:48.614080 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref++: 2 
10:32:48.614089 11844 11844 D IJKMEDIA: ijkmp_stop_and_wait()
10:32:48.614111 11844 11844 D IJKMEDIA: ijkmp_stop_l()
10:32:48.614121 11844 11844 D IJKMEDIA: ijkmp_stop_and_wait()=-3
10:32:48.614147 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref--: 1 
10:32:48.614156 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_stop() 
10:32:48.614167 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_reset() 
10:32:48.614176 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref++: 2 
10:32:48.614239 11844 11844 I IJKMEDIA: ijkmp_set_weak_thiz , mp[0xb400007b6c8a4230],weak_thiz[0x0]
10:32:48.614254 11844 11844 I IJKMEDIA: IjkMediaPlayer_reset() weak_this[0x2d9a]
10:32:48.614261 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_release() 
10:32:48.614275 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref++: 3 
10:32:48.614282 11844 11844 D IJKMEDIA: ijkmp_set_android_surface(surface=0x0)
10:32:48.614290 11844 11844 D IJKMEDIA: ffpipeline_set_surface()
10:32:48.614297 11844 11844 D IJKMEDIA: ijkmp_set_android_surface(surface=0x0)=void
10:32:48.614303 11844 11844 I IJKMEDIA: ijkmp_set_weak_thiz , mp[0xb400007b6c8a4230],weak_thiz[0x0]
10:32:48.614310 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref--: 2 
10:32:48.614316 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref--: 1 
10:32:48.614322 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_release() 
10:32:48.614328 11844 11844 I IJKMEDIA: FUNC >> IjkMediaPlayer_native_setup() 
10:32:48.614334 11844 11844 I IJKMEDIA: ijkmp_android_create() weak_this[0x2d9a]
10:32:48.614343 11844 11844 I IJKMEDIA: ffp_create ffp_create FFPlayer[0xb400007bdc850390]
10:32:48.614353 11844 11844 D IJKMEDIA: loudness_context_create()
10:32:48.614528 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 1 
10:32:48.614543 11844 11844 D IJKMEDIA: ffpipeline_create_from_android()
10:32:48.614555 11844 11844 I IJKMEDIA: ijkmp_set_weak_thiz , mp[0xb400007b6c89f610],weak_thiz[0x3336]
10:32:48.614564 11844 11844 D IJKMEDIA: ijkmp_set_inject_opaque(0x3336)
10:32:48.614575 11844 11844 D IJKMEDIA: ijkmp_set_inject_opaque()=void
10:32:48.614582 11844 11844 D IJKMEDIA: ijkmp_set_ijkio_inject_opaque(0x3336)
10:32:48.614733 11844 13640 D IJKMEDIA: afade - timer thread: begin 
10:32:48.615232 11844 11844 D IJKMEDIA: ijkmp_set_ijkio_inject_opaque()=void
10:32:48.615250 11844 11844 D IJKMEDIA: ijkmp_android_set_mediacodec_select_callback()
10:32:48.615259 11844 11844 D IJKMEDIA: ffpipeline_set_mediacodec_select_callback
10:32:48.615265 11844 11844 D IJKMEDIA: ijkmp_android_set_mediacodec_select_callback()=void
10:32:48.615404 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_native_setup() 
10:32:48.615416 11844 11844 V IJKMEDIA: mp[0xb400007b6c8a4230] ref--: 0 
10:32:48.615424 11844 11844 D IJKMEDIA: ijkmp_dec_ref(): ref=0
10:32:48.615433 11844 11844 D IJKMEDIA: ijkmp_shutdown_l()
10:32:48.615441 11844 11844 D IJKMEDIA: ijkmp_shutdown_l()=void
10:32:48.615509 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_reset() 
10:32:48.615543 11844 11844 I player_log_tag: IjkMediaPlayer.setAutoPlayOnPrepared->enabled= false
10:32:48.615555 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_setAutoPlayOnPrepared(0) 
10:32:48.615575 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 2 
10:32:48.615585 11844 12549 I IJKMEDIA: destroyCallback start destroy FFPlayer[0xb400007bdc857100]
10:32:48.616011 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 1 
10:32:48.616125 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_setAutoPlayOnPrepared() 
10:32:48.616144 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_clearProxyAdress() 
10:32:48.616153 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 2 
10:32:48.616161 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 1 
10:32:48.616167 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_clearProxyAdress() 
10:32:48.616176 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_setAudioFadeEnable(0) 
10:32:48.616182 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 2 
10:32:48.616188 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 1 
10:32:48.616194 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_setAudioFadeEnable() 
10:32:48.616218 11844 11844 I player_log_tag: IjkMediaPlayer.setDataSource->setDataSource: https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3
10:32:48.616231 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_setDataSourceAndHeaders() 
10:32:48.616239 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 2 
10:32:48.616246 11844 11844 V IJKMEDIA: setDataSource: path https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3
10:32:48.616254 11844 11844 D IJKMEDIA: ijkmp_set_data_source(url="https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3")
10:32:48.616261 11844 11844 D IJKMEDIA: ijkmp_change_state_l:1
10:32:48.616268 11844 11844 D IJKMEDIA: ijkmp_set_data_source(url="https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3")=0
10:32:48.616276 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 1 
10:32:48.616283 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_setDataSourceAndHeaders() 
10:32:48.616303 11844 11844 I player_log_tag: IjkMediaPlayer.setDuration->duration =  128444
10:32:48.616315 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_setDuration(128444, 128444) 
10:32:48.616322 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 2 
10:32:48.616329 11844 11844 D IJKMEDIA: ijkmp_set_duration(128444, 128444)
10:32:48.616337 11844 11844 I IJKMEDIA: set entire duration : 128444, 
10:32:48.616346 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_setDuration() 
10:32:48.616448 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 1 
10:32:48.616460 11844 12549 D IJKMEDIA: loudness_context_destroy()
10:32:48.616475 11844 11844 I player_log_tag: IjkMediaPlayer.prepare->needSeek = 0
10:32:48.616490 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_prepareAsync(0, 3) 
10:32:48.616499 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 2 
10:32:48.616507 11844 11844 D IJKMEDIA: ijkmp_prepare_async()
10:32:48.616514 11844 11844 D IJKMEDIA: ijkmp_change_state_l:2
10:32:48.616524 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 3 
10:32:48.616533 11844 11844 I IJKMEDIA: SDL_CreateThreadEx: ff_msg_loop
10:32:48.616748 11844 11844 I IJKMEDIA: SDL_CreateThreadEx: ff_msg_loop over
10:32:48.616845 11844 13646 I IJKMEDIA: SDL_RunThread: [13646] ff_msg_loop
10:32:48.617267 11844 13646 D IJKMEDIA: message_loop 
10:32:48.617296 11844 11844 I IJKMEDIA: ===== versions =====
10:32:48.617314 11844 11844 I IJKMEDIA: ijkplayer    : k0.8.8-226-g5bb1d3d
10:32:48.617323 11844 11844 I IJKMEDIA: ffmpeg       : ff3.4--ijk0.8.8--20230901--001
10:32:48.617339 11844 11844 I IJKMEDIA: libavutil    : 55.78.100
10:32:48.617347 11844 11844 I IJKMEDIA: libavcodec   : 57.107.100
10:32:48.617353 11844 11844 I IJKMEDIA: libavformat  : 57.83.100
10:32:48.617371 11844 11844 I IJKMEDIA: libswscale   : 4.8.100
10:32:48.617378 11844 11844 I IJKMEDIA: libswresample: 2.9.100
10:32:48.617383 11844 11844 I IJKMEDIA: ===================
10:32:48.617530 11844 11844 I IJKMEDIA: ===== options =====
10:32:48.617539 11844 11844 I IJKMEDIA: player-opts : start-on-prepared            = 0
10:32:48.617547 11844 11844 I IJKMEDIA: format-opts : ijkapplication               = 0xb400007aec894c00
10:32:48.617553 11844 11844 I IJKMEDIA: format-opts : ijkiomanager                 = 0xb400007bac85da60
10:32:48.617559 11844 11844 I IJKMEDIA: format-opts : max_reinit_input_times       = 5
10:32:48.617565 11844 11844 I IJKMEDIA: format-opts : reconnect                    = 1
10:32:48.617571 11844 11844 I IJKMEDIA: format-opts : timeout                      = 20000000
10:32:48.617577 11844 11844 I IJKMEDIA: format-opts : connect_timeout              = 20000000
10:32:48.617583 11844 11844 I IJKMEDIA: format-opts : addrinfo_timeout             = 2000000
10:32:48.617589 11844 11844 I IJKMEDIA: format-opts : dns_cache_timeout            = 300000
10:32:48.617595 11844 11844 I IJKMEDIA: ===================
10:32:48.617742 11844 13646 D IJKMEDIA: message_loop_n mp[0xb400007b6c89f610]
10:32:48.617760 11844 13646 I IJKMEDIA: message_loop_n will run into the message loop 
10:32:48.617845 11844 13646 D IJKMEDIA: FFP_MSG_FLUSH:
10:32:48.617907 11844 13646 I player_log_tag: message = 0, arg1 = 0, arg2 = 0
10:32:48.618249 11844 11844 I IJKMEDIA: SDL_CreateThreadEx: ff_vout
10:32:48.618411 11844 11844 I IJKMEDIA: SDL_CreateThreadEx: ff_vout over
10:32:48.618444 11844 11844 I IJKMEDIA: SDL_CreateThreadEx: ff_read
10:32:48.618569 11844 13647 I IJKMEDIA: SDL_RunThread: [13647] ff_vout
10:32:48.618717 11844 11844 I IJKMEDIA: SDL_CreateThreadEx: ff_read over
10:32:48.618739 11844 11844 D IJKMEDIA: ijkmp_prepare_async()=0
10:32:48.618747 11844 11844 I IJKMEDIA: FUNC >> ijkmp_set_content_type()
10:32:48.618756 11844 11844 I IJKMEDIA: ffp_set_ContentTypeValue content_type=2
10:32:48.618784 11844 11844 I IJKMEDIA: FUNC << ijkmp_set_content_type()
10:32:48.618791 11844 11844 I IJKMEDIA: ffp_set_UsageTypeValue usage_type=1
10:32:48.618804 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 2 
10:32:48.618811 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_prepareAsync() 
10:32:48.618851 11844 13648 I IJKMEDIA: SDL_RunThread: [13648] ff_read
10:32:48.618879 11844 13648 D IJKMEDIA: read_thread 
10:32:48.618888 11844 13648 I IJKMEDIA: will allocate avformat context 
10:32:48.618903 11844 13648 I IJKMEDIA: will set header in HttpContext for dns
10:32:48.618910 11844 13648 I IJKMEDIA: read_thread start probe if encoded
10:32:48.618921 11844 13648 I IJKMEDIA: https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3 start probe if encoded
10:32:48.619099 11844 13648 I IJKLIBAV: get cache entry, hostname = iovimage.radio.cn, portstr = 443
10:32:48.619320 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] onPlayerPreparingPlayItem{audioId=1015809379411, playUrl='https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3', playUrlId='9223371021045396396', position=0, buyType=0, buyStatus=0, audition=0, vip=0, fine=0, duration=128444, mDataMap={}, listenCount=9328049, payMethod=[com.kaolafm.opensdk.api.media.model.PayMethod@769d1d1], type=0}
10:32:48.623652 11844 12550 D IJKMEDIA: afade - timer thread: finish 
10:32:48.623810 11844 12549 I IJKMEDIA: destroyCallback start destroy end
10:32:48.623879 11844 12549 I IJKMEDIA: destroyCallback destroyThread wait
10:32:48.627490 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [NetWorkUtils user0] networkState=1
10:32:48.636249 11844 13648 I IJKLIBAV: Add dns cache hostname = iovimage.radio.cn, portstr = 443
10:32:48.724863 11844 13648 I IJKMEDIA: https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3 avio_open success
10:32:48.724902 11844 13648 I IJKMEDIA: probe_data_encode of 128 size
10:32:48.724911 11844 13648 I IJKMEDIA: read_thread after probe if encoded: 0
10:32:48.724921 11844 13648 I IJKMEDIA: will open input file: https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3
10:32:48.725241 11844 13648 I IJKLIBAV: ID3v2.0 tag skipped, cannot handle version
10:32:48.725415 11844 13648 I IJKLIBAV: get cache entry, hostname = iovimage.radio.cn, portstr = 443
10:32:48.725466 11844 13648 I IJKLIBAV: Hit DNS Cache hostname = iovimage.radio.cn, ip = **************
10:32:48.831134 11844 13648 I IJKLIBAV: get cache entry, hostname = iovimage.radio.cn, portstr = 443
10:32:48.831156 11844 13648 I IJKLIBAV: Hit DNS Cache hostname = iovimage.radio.cn, ip = **************
10:32:48.925427 11844 13648 I IJKMEDIA: after open input file: 0
10:32:48.925498 11844 13648 W IJKMEDIA: Option ijkapplication not found.
10:32:48.925509 11844 13648 I IJKMEDIA: get url duration: 0(us) 
10:32:48.925517 11844 13648 I IJKMEDIA: has assigned custom duration: 128444 
10:32:48.925526 11844 13648 D IJKMEDIA: set ic->duration from  -9223372036854775808(us) to  128444000(us) 
10:32:48.925591 11844 13648 I IJKMEDIA: will find stream information 
10:32:48.925927 11844 13646 D IJKMEDIA: FFP_MSG_OPEN_INPUT:
10:32:48.925997 11844 13646 I player_log_tag: message = 200, arg1 = 10005, arg2 = 0
10:32:48.926669 11844 13648 I IJKMEDIA: will find loudness information 
10:32:48.926720 11844 13648 I IJKMEDIA: streams[0] no loudness info found
10:32:48.926731 11844 13648 I IJKMEDIA: max_frame_duration: 10.000
10:32:48.926758 11844 13648 I IJKLIBAV: Input #0, mp3, from 'https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3':
10:32:48.926811 11844 13648 I IJKLIBAV:   Duration: 
10:32:48.926819 11844 13648 I IJKLIBAV: 00:02:08.44
10:32:48.926827 11844 13648 I IJKLIBAV: , start: 
10:32:48.926834 11844 13648 I IJKLIBAV: 0.000000
10:32:48.926841 11844 13648 I IJKLIBAV: , bitrate: 
10:32:48.926848 11844 13648 I IJKLIBAV: 31 kb/s
10:32:48.926855 11844 13648 I IJKLIBAV: 
10:32:48.926878 11844 13646 D IJKMEDIA: FFP_MSG_FIND_STREAM_INFO:
10:32:48.926955 11844 13646 I player_log_tag: message = 200, arg1 = 10006, arg2 = 0
10:32:48.927054 11844 13648 I IJKLIBAV:     Stream #0:0
10:32:48.927090 11844 13648 I IJKLIBAV: : Audio: mp3, 22050 Hz, stereo, s16p, 32 kb/s
10:32:48.927099 11844 13648 I IJKLIBAV: 
10:32:48.927111 11844 13648 I IJKMEDIA: will open the streams 
10:32:48.927160 11844 13648 I IJKMEDIA: wanted_spec.usage_type 1 wanted_spec.content_type 2
10:32:48.927175 11844 13648 I IJKMEDIA:  SDL_Android_AudioTrack_new_from_spec
10:32:48.927181 11844 13648 I IJKMEDIA: SDL_Android_AudioTrack: CHANNEL_OUT_STEREO
10:32:48.927187 11844 13648 I IJKMEDIA: SDL_Android_AudioTrack: ENCODING_PCM_16BIT
10:32:48.942804 11844 13648 I IJKMEDIA: SDL_Android_AudioTrack_new_from_spec: setUsage=1,setContentType=2
10:32:48.943130 11844 13648 D AudioTrack: parseTrackConfig 4076
10:32:48.943225 11844 13648 E AudioTrack: Failed to parse /vendor/etc/audio_ar/audio_framework_process.xml: Tinyxml2 error (3): Error=XML_ERROR_FILE_NOT_FOUND ErrorID=3 (0x3) Line number=0: filename=/vendor/etc/audio_ar/audio_framework_process.xml
10:32:48.943240 11844 13648 I AudioTrack: set(): streamType -1, sampleRate 22050, format 0x1, channelMask 0x3, frameCount 3544, flags #0, notificationFrames 0, sessionId 0, transferType 3, uid -1, pid -1
10:32:48.943249 11844 13648 I AudioTrack: set(): Building AudioTrack with attributes: usage=1 content=2 flags=0xa00 tags=[]
10:32:48.963033 11844 13648 I IJKMEDIA: SDL_Android_AudioTrack_new_from_spec: getUsage=1,getContentType=2
10:32:48.963060 11844 13648 I IJKMEDIA: SDL_Android_AudioTrack_new_from_spec: init volume as 1.000000/(0.000000,1.000000)
10:32:48.963122 11844 13648 I IJKMEDIA: audio_session_id = 94393
10:32:48.963135 11844 13648 I IJKMEDIA: SDL_CreateThreadEx: ff_aout_android
10:32:48.963292 11844 13648 I IJKMEDIA: SDL_CreateThreadEx: ff_aout_android over
10:32:48.963321 11844 13648 I IJKMEDIA: AudioCodec: avcodec, mp3
10:32:48.963334 11844 13656 I IJKMEDIA: SDL_RunThread: [13656] ff_aout_android
10:32:48.963386 11844 13648 I IJKMEDIA: SDL_CreateThreadEx: ff_audio_dec
10:32:48.963508 11844 13648 I IJKMEDIA: SDL_CreateThreadEx: ff_audio_dec over
10:32:48.963671 11844 13646 D IJKMEDIA: FFP_MSG_COMPONENT_OPEN:
10:32:48.963711 11844 13646 I player_log_tag: message = 200, arg1 = 10007, arg2 = 0
10:32:48.964475 11844 13656 I PlayerBase: baseStart() piid=33183, uid=10040, pid=11844, name=com.voyah.cockpit.voyahmusic, Callers=android.media.AudioTrack.startImpl:2957 android.media.AudioTrack.play:2928 <bottom of call stack> 
10:32:48.974968 11844 13646 D IJKMEDIA: FFP_MSG_AUDIO_RENDERING_START:
10:32:48.975017 11844 13646 I player_log_tag: message = 200, arg1 = 10002, arg2 = 0
10:32:48.975032 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_MSG_PREPARED
10:32:48.975041 11844 13646 D IJKMEDIA: ijkmp_change_state_l:3
10:32:48.975050 11844 13646 D IJKMEDIA: ijkmp_change_state_l:5
10:32:48.975059 11844 13646 D IJKMEDIA: FFP_MSG_PREPARED:
10:32:48.975071 11844 13646 I player_log_tag: message = 1, arg1 = 0, arg2 = 0
10:32:48.975113 11844 13646 I player_log_tag: PlayerService$2.onPlayerPreparingComplete->mIPlayerStateCoreListener.onPlayerPreparingComplete
10:32:48.976673 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] onPlayerPreparingCompletePlayItem{audioId=1015809379411, playUrl='https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3', playUrlId='9223371021045396396', position=0, buyType=0, buyStatus=0, audition=0, vip=0, fine=0, duration=128444, mDataMap={}, listenCount=9328049, payMethod=[com.kaolafm.opensdk.api.media.model.PayMethod@769d1d1], type=0}
10:32:48.976756 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onmediainfochanged
10:32:48.977126 11844 11844 I player_log_tag: PlayerManager.playOnly->run
10:32:48.977195 11844 11844 I player_log_tag: PlayerCustomizeManager.disposePlay->isDispose = false
10:32:48.977218 11844 11844 I player_log_tag: PlayerService.checkAudioFocus->isLoseAudioFocus = true
10:32:48.977232 11844 11844 I player_log_tag: PlayerService.checkAudioFocus->no need
10:32:48.977255 11844 11844 I player_log_tag: IjkMediaPlayer.play->play
10:32:48.977274 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_start() 
10:32:48.977445 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 3 
10:32:48.977457 11844 11844 D IJKMEDIA: ijkmp_start()
10:32:48.977464 11844 11844 D IJKMEDIA: ijkmp_start_l()
10:32:48.977471 11844 11844 D IJKMEDIA: ikjmp_chkst_start_l--->mp_state:5
10:32:48.977541 11844 11844 D IJKMEDIA: ijkmp_start()=0
10:32:48.977568 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 2 
10:32:48.977575 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_start() 
10:32:48.977829 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] saveYtContinueMediaInfo albumId=1100002156582,songId=1015809379411,sort=0
10:32:48.979078 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA page_size" took 0.000 ms
10:32:48.979127 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:48.979453 11844 12233 V SQLiteLog: (283) recovered 54 frames from WAL file /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db-wal
10:32:48.979556 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_mode" took 0.000 ms
10:32:48.979624 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA synchronous" took 0.000 ms
10:32:48.979644 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:48.979659 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:48.979675 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:48.979690 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:48.979740 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:48.979792 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:48.979953 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA page_size" took 0.000 ms
10:32:48.979969 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:48.980258 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_mode" took 0.000 ms
10:32:48.980308 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA synchronous" took 0.000 ms
10:32:48.980327 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:48.980343 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:48.980359 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:48.980374 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:48.980416 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:48.980462 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:48.980481 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA user_version;" took 0.000 ms
10:32:48.980518 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA user_version;" took 0.000 ms
10:32:48.980586 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "SELECT 1 FROM sqlite_master WHERE type = 'table' AND name='room_master_table'" took 0.000 ms
10:32:48.980601 11844 12233 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:48.980664 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "SELECT identity_hash FROM room_master_table WHERE id = 42 LIMIT 1" took 0.000 ms
10:32:48.980676 11844 12233 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:48.980717 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA temp_store = MEMORY;" took 0.000 ms
10:32:48.980738 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA recursive_triggers='ON';" took 0.000 ms
10:32:48.980863 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "CREATE TEMP TABLE room_table_modification_log (table_id INTEGER PRIMARY KEY, invalidated INTEGER NOT NULL DEFAULT 0)" took 0.000 ms
10:32:48.981055 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA page_size" took 0.000 ms
10:32:48.981072 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:48.981155 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_mode" took 0.000 ms
10:32:48.981644 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA synchronous" took 0.000 ms
10:32:48.981665 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:48.981682 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:48.981699 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:48.981721 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA page_size" took 0.000 ms
10:32:48.981790 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:48.981806 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:48.981848 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:48.982134 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 1.000 ms
10:32:48.982204 11844 13659 V SQLiteLog: (283) recovered 7 frames from WAL file /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db-wal
10:32:48.982266 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "SELECT * FROM yt_continuation_mediaInfo WHERE albumId = ?" took 0.000 ms
10:32:48.982280 11844 12233 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:48.982296 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_mode" took 0.000 ms
10:32:48.982371 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA synchronous" took 0.000 ms
10:32:48.982399 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:48.982440 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:48.982478 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:48.982504 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:48.982524 11844 13633 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:48.982572 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "UPDATE OR ABORT `yt_continuation_mediaInfo` SET `id` = ?,`albumId` = ?,`songId` = ?,`sort` = ?,`userId` = ? WHERE `id` = ?" took 0.000 ms
10:32:48.982647 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:48.982726 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:48.982798 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:48.982979 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA page_size" took 0.000 ms
10:32:48.983001 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:48.983132 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_mode" took 0.000 ms
10:32:48.983200 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA synchronous" took 0.000 ms
10:32:48.983226 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:48.983246 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:48.983267 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:48.983286 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:48.983334 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:48.983398 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:48.983431 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA user_version;" took 0.000 ms
10:32:48.983596 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA user_version;" took 0.000 ms
10:32:48.983731 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT 1 FROM sqlite_master WHERE type = 'table' AND name='room_master_table'" took 0.000 ms
10:32:48.983759 11844 13659 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:48.983857 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT identity_hash FROM room_master_table WHERE id = 42 LIMIT 1" took 0.000 ms
10:32:48.983877 11844 13659 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:48.983938 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA temp_store = MEMORY;" took 0.000 ms
10:32:48.983971 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA recursive_triggers='ON';" took 0.000 ms
10:32:48.984608 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] onPlayerPreparingComplete : playPosition is:1, list size is : 20
10:32:48.984670 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "CREATE TEMP TABLE room_table_modification_log (table_id INTEGER PRIMARY KEY, invalidated INTEGER NOT NULL DEFAULT 0)" took 0.000 ms
10:32:48.984793 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=0;duration=128444
10:32:48.984820 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=0
10:32:48.984906 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:32:48.985091 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA page_size" took 0.000 ms
10:32:48.985117 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA foreign_keys" took 0.000 ms
10:32:48.985247 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_mode" took 0.000 ms
10:32:48.985334 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA synchronous" took 0.000 ms
10:32:48.985363 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_size_limit" took 0.000 ms
10:32:48.985419 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA journal_size_limit=524288" took 0.000 ms
10:32:48.985448 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA wal_autocheckpoint" took 0.000 ms
10:32:48.985467 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "PRAGMA wal_autocheckpoint=100" took 0.000 ms
10:32:48.985525 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "CREATE TABLE IF NOT EXISTS android_metadata (locale TEXT)" took 0.000 ms
10:32:48.985590 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT locale FROM android_metadata UNION SELECT NULL ORDER BY locale DESC LIMIT 1" took 0.000 ms
10:32:48.985701 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM yt_play_history_list WHERE audioId = ?" took 0.000 ms
10:32:48.985726 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_REQ_START
10:32:48.985751 11844 13659 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:48.986396 11844 13659 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  seekToImpl progress: 6084
10:32:48.986428 11844 13659 D AudioZoneSwitch: getCurZoneIdByUid uid 10040
10:32:48.986790 11844 13646 D IJKMEDIA: ikjmp_chkst_start_l--->mp_state:5
10:32:48.986805 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_REQ_START: start on fly
10:32:48.986815 11844 13646 D IJKMEDIA: ijkmp_change_state_l:4
10:32:48.986826 11844 13646 D IJKMEDIA: FFP_MSG_STARTED:
10:32:48.986854 11844 13646 I player_log_tag: message = 10, arg1 = 0, arg2 = 0
10:32:48.988301 11844 13657 I IJKMEDIA: SDL_RunThread: [13657] ff_audio_dec
10:32:48.990443 11844 11875 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getProgressImpl
10:32:48.990928 11844 12233 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "COMMIT;" took 8.000 ms
10:32:48.991506 11844 12233 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] saveYtContinueMediaInfo=YtContinueMediaInfo{id=1, albumId='1100002156582', songId='1015809379411', sort=0, userId=null}
10:32:48.991639 11844 13662 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:48.991786 11844 13662 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "SELECT * FROM room_table_modification_log WHERE invalidated = 1;" took 0.000 ms
10:32:48.991810 11844 13662 D SQLiteCursor: received count(*) from native_fill_window: 0
10:32:48.991866 11844 13662 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_continuation_mediaInfo.db: "COMMIT;" took 0.000 ms
10:32:48.992740 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM yt_play_history_list WHERE audioId = ?" took 0.000 ms
10:32:48.992760 11844 13661 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:48.992825 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:48.992928 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "UPDATE OR ABORT `yt_play_history_list` SET `id` = ?,`mediaId` = ?,`audioId` = ?,`progress` = ? WHERE `id` = ?" took 0.000 ms
10:32:48.995664 11844 13648 I IJKMEDIA: will run into the read loop 
10:32:48.995686 11844 13648 V IJKMEDIA: read thread: run in read loop - 166245143
10:32:48.997359 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 5.000 ms
10:32:49.002259 11844 13633 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/GETLIKE clientHost =media_manager code =0 token =28c110f5-0e94-4fbc-b70a-c68524587cb1
10:32:49.002417 11844 13633 I aaccdd1 : ipc:: onResponse: url = yt_music/mediabridgesm/GETLIKE clientHost =media_manager code =0 token =72fa2018-09b8-4549-986c-4d95f75edf2b
10:32:49.004240 11844 13659 D AudioZoneSwitch: getZoneIdByDisplayId displayId 0
10:32:49.005434 11844 12387 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:49.005498 11844 12387 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:49.005512 11844 12387 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:49.005529 11844 12387 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:49.005634 11844 12387 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:49.005646 11844 12387 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:49.005668 11844 12387 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:49.006610 11844 12390 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:32:49.006794 11844 12390 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "INSERT OR REPLACE INTO "CONFIG_DATA" ("_id","TYPE","JSON") VALUES (?,?,?)" took 0.000 ms
10:32:49.009144 11844 12387 D okhttp.Http2: >> 0x0000001b   405 HEADERS       END_HEADERS
10:32:49.009394 11844 12387 D okhttp.Http2: >> 0x0000001b   149 DATA          END_STREAM
10:32:49.010408 11844 13659 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] setZoneId  lastZoneId 0  ,displayId 0  ,getZoneId 0
10:32:49.011172 11844 13663 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:49.011510 11844 13663 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM room_table_modification_log WHERE invalidated = 1;" took 0.000 ms
10:32:49.011533 11844 13663 D SQLiteCursor: received count(*) from native_fill_window: 0
10:32:49.011587 11844 13663 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 0.000 ms
10:32:49.013058 11844 13659 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] request in mHasFocus = true,requestResult=1
10:32:49.013105 11844 13659 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/audioSuccess
10:32:49.013654 11844 13659 I player_log_tag: PlayerManager.seek->position = 6084
10:32:49.013701 11844 13659 I player_log_tag: IjkMediaPlayer.seek->msec = 6084
10:32:49.013720 11844 13659 D IJKMEDIA: FUNC >> IjkMediaPlayer_seekTo(6084) 
10:32:49.013732 11844 13659 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 3 
10:32:49.013742 11844 13659 D IJKMEDIA: ijkmp_seek_to(6084)
10:32:49.013751 11844 13659 I IJKMEDIA: ijkmp_seek_to_l(6084), duration(0), seekMsec(6084)
10:32:49.013767 11844 13659 D IJKMEDIA: ijkmp_seek_to(6084)=0
10:32:49.013774 11844 13659 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 2 
10:32:49.013781 11844 13659 D IJKMEDIA: FUNC << IjkMediaPlayer_seekTo() 
10:32:49.014802 11844 11844 I report_tag: │ timer = 60
10:32:49.015221 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_REQ_SEEK
10:32:49.015533 11844 13646 D IJKMEDIA: ffp_seek_to_l: 6084000 = 6084000(6084) + 0
10:32:49.015563 11844 13646 D IJKMEDIA: stream_seek: new seek, seek pos = 6084000
10:32:49.015572 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_REQ_SEEK: seek to 6084
10:32:49.016197 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] onPlayerPlayingPlayItem{audioId=1015809379411, playUrl='https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3', playUrlId='9223371021045396396', position=0, buyType=0, buyStatus=0, audition=0, vip=0, fine=0, duration=128444, mDataMap={}, listenCount=9328049, payMethod=[com.kaolafm.opensdk.api.media.model.PayMethod@769d1d1], type=0}
10:32:49.016235 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onplaystatechanged
10:32:49.016259 11844 13665 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:32:49.016405 11844 11844 I IPCServiceEngine: notify: url = yt_music/ytloginsm/onopenvip userId = 0
10:32:49.016489 11844 13665 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "INSERT INTO "REPORT_DATA" ("_id","TYPE","SEND_STR") VALUES (?,?,?)" took 0.000 ms
10:32:49.017689 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtLoginSM user0]  login = false 未登录
10:32:49.018076 11844 12255 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:49.018105 11844 12255 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:49.018117 11844 12255 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:49.018132 11844 12255 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:49.018234 11844 12255 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:49.018248 11844 12255 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:49.018273 11844 12255 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:49.021784 11844 12255 D okhttp.Http2: >> 0x0000001d   377 HEADERS       END_HEADERS
10:32:49.021915 11844 12255 D okhttp.Http2: >> 0x0000001d   149 DATA          END_STREAM
10:32:49.022643 11844 12390 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "COMMIT;" took 16.000 ms
10:32:49.022874 11844 12390 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "SELECT COUNT(*) FROM "CONFIG_DATA"" took 0.000 ms
10:32:49.030553 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:49.032161 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:49.037254 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:49.039021 11844 13665 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "COMMIT;" took 23.000 ms
10:32:49.039141 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:49.039205 11844 13665 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:32:49.064603 11844 12406 D okhttp.Http2: << 0x0000001b   193 HEADERS       END_HEADERS
10:32:49.065108 11844 12406 D okhttp.Http2: << 0x0000001b    97 DATA          
10:32:49.065251 11844 12406 D okhttp.Http2: << 0x0000001b     0 DATA          END_STREAM
10:32:49.104164 11844 12406 D okhttp.Http2: << 0x0000001d   193 HEADERS       END_HEADERS
10:32:49.104648 11844 12406 D okhttp.Http2: << 0x0000001d    96 DATA          
10:32:49.104885 11844 12406 D okhttp.Http2: << 0x0000001d     0 DATA          END_STREAM
10:32:49.105459 11844 12255 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:49.108492 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:49.108827 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:49.108986 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 479 µs: OkHttp ConnectionPool
10:32:49.121121 11844 13656 D IJKMEDIA: avcodec/Audio: first frame decoded
10:32:49.124108 11844 13646 D IJKMEDIA: FFP_MSG_AUDIO_DECODED_START:
10:32:49.124182 11844 13646 I player_log_tag: message = 200, arg1 = 10003, arg2 = 0
10:32:50.441321 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=1002;duration=128444
10:32:50.441396 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=1002
10:32:50.441436 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:32:51.521795 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=2081;duration=128444
10:32:51.521924 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mAudioPlayedTime=1079
10:32:51.521935 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=2081
10:32:51.521958 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:32:51.522703 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM yt_play_history_list WHERE audioId = ?" took 0.000 ms
10:32:51.522769 11844 13661 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:51.522865 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:51.522920 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "UPDATE OR ABORT `yt_play_history_list` SET `id` = ?,`mediaId` = ?,`audioId` = ?,`progress` = ? WHERE `id` = ?" took 0.000 ms
10:32:51.523149 11844 13665 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:51.523167 11844 13665 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:51.523177 11844 13665 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:51.523187 11844 13665 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:51.523269 11844 13665 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:51.523278 11844 13665 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:51.523294 11844 13665 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:51.523974 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 1.000 ms
10:32:51.524297 11844 13638 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:51.524557 11844 13638 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM room_table_modification_log WHERE invalidated = 1;" took 0.000 ms
10:32:51.525026 11844 13638 D SQLiteCursor: received count(*) from native_fill_window: 0
10:32:51.525076 11844 13638 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 0.000 ms
10:32:51.525208 11844 13665 D okhttp.Http2: >> 0x0000001f   380 HEADERS       END_HEADERS
10:32:51.525438 11844 13665 D okhttp.Http2: >> 0x0000001f   152 DATA          END_STREAM
10:32:51.618061 11844 12406 D okhttp.Http2: << 0x0000001f   193 HEADERS       END_HEADERS
10:32:51.618810 11844 12406 D okhttp.Http2: << 0x0000001f    96 DATA          
10:32:51.619010 11844 12406 D okhttp.Http2: << 0x0000001f     0 DATA          END_STREAM
10:32:51.620755 11844 13665 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:51.620886 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:51.620978 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:51.621406 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 521 µs: OkHttp ConnectionPool
10:32:52.481899 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=3039;duration=128444
10:32:52.482029 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mAudioPlayedTime=2037
10:32:52.482090 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=3039
10:32:52.482124 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:32:53.441761 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=4000;duration=128444
10:32:53.441816 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mAudioPlayedTime=2998
10:32:53.441832 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=4000
10:32:53.441858 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:32:54.521731 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=5080;duration=128444
10:32:54.521812 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mAudioPlayedTime=4078
10:32:54.521839 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=5080
10:32:54.521866 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:32:54.522387 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM yt_play_history_list WHERE audioId = ?" took 0.000 ms
10:32:54.522436 11844 13659 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:54.522581 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:54.522662 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "UPDATE OR ABORT `yt_play_history_list` SET `id` = ?,`mediaId` = ?,`audioId` = ?,`progress` = ? WHERE `id` = ?" took 0.000 ms
10:32:54.523990 11844 12390 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:54.524086 11844 12390 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:54.524107 11844 12390 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:54.524128 11844 12390 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:54.524552 11844 12390 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:54.524570 11844 12390 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:54.524596 11844 12390 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:54.525181 11844 13659 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 3.000 ms
10:32:54.525504 11844 13639 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:54.525605 11844 13639 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM room_table_modification_log WHERE invalidated = 1;" took 0.000 ms
10:32:54.525621 11844 13639 D SQLiteCursor: received count(*) from native_fill_window: 0
10:32:54.525655 11844 13639 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 0.000 ms
10:32:54.526977 11844 12390 D okhttp.Http2: >> 0x00000021   377 HEADERS       END_HEADERS
10:32:54.527171 11844 12390 D okhttp.Http2: >> 0x00000021   152 DATA          END_STREAM
10:32:54.577330 11844 12406 D okhttp.Http2: << 0x00000021   193 HEADERS       END_HEADERS
10:32:54.578131 11844 12406 D okhttp.Http2: << 0x00000021    96 DATA          
10:32:54.578366 11844 12406 D okhttp.Http2: << 0x00000021     0 DATA          END_STREAM
10:32:54.578930 11844 12390 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:54.579056 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:54.579117 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:54.579166 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 112 µs: OkHttp ConnectionPool
10:32:54.791696 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  pauseImpl
10:32:54.791822 11844 12262 I player_log_tag: PlayerManager.pause->fromUser = true
10:32:54.791856 11844 12262 I PlayerService: pauseInner
10:32:54.791891 11844 12262 I player_log_tag: IjkMediaPlayer.pause->pause
10:32:54.791938 11844 12262 D IJKMEDIA: FUNC >> IjkMediaPlayer_pause() 
10:32:54.791951 11844 12262 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 3 
10:32:54.791959 11844 12262 D IJKMEDIA: ijkmp_pause()
10:32:54.791981 11844 12262 D IJKMEDIA: ijkmp_pause_l()
10:32:54.792012 11844 12262 D IJKMEDIA: ijkmp_pause()=0
10:32:54.792129 11844 12262 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 2 
10:32:54.792153 11844 12262 D IJKMEDIA: FUNC << IjkMediaPlayer_pause() 
10:32:54.792169 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_REQ_PAUSE
10:32:54.792189 11844 13646 D IJKMEDIA: ijkmp_change_state_l:5
10:32:54.792200 11844 13646 D IJKMEDIA: FFP_MSG_PAUSED:
10:32:54.792226 11844 13646 I player_log_tag: message = 11, arg1 = 0, arg2 = 0
10:32:54.792443 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] onPlayerPausedPlayItem{audioId=1015809379411, playUrl='https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3', playUrlId='9223371021045396396', position=5080, buyType=0, buyStatus=0, audition=0, vip=0, fine=0, duration=128444, mDataMap={}, listenCount=9328049, payMethod=[com.kaolafm.opensdk.api.media.model.PayMethod@769d1d1], type=0}
10:32:54.792497 11844 12262 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onplaystatechanged
10:32:54.792606 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] onPlayerPausedPlayItem{audioId=1015809379411, playUrl='https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3', playUrlId='9223371021045396396', position=5080, buyType=0, buyStatus=0, audition=0, vip=0, fine=0, duration=128444, mDataMap={}, listenCount=9328049, payMethod=[com.kaolafm.opensdk.api.media.model.PayMethod@769d1d1], type=0}
10:32:54.792630 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onplaystatechanged
10:32:54.800738 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:54.803798 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:54.811927 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:54.814343 11844 12262 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:59.006468 11844 13648 V IJKMEDIA: read thread: run in read loop - 166255155
10:32:59.006560 11844 13648 I IJKMEDIA: process seek request, target seek position: 6084000 
10:32:59.006570 11844 13648 D IJKMEDIA: ffp_toggle_buffering_l: start
10:32:59.006584 11844 13648 I IJKMEDIA: seek in cache queue 
10:32:59.006599 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_MSG_BUFFERING_START
10:32:59.006618 11844 13646 D IJKMEDIA: FFP_MSG_BUFFERING_START:
10:32:59.006643 11844 13646 I player_log_tag: message = 200, arg1 = 701, arg2 = 1
10:32:59.006847 11844 13646 I NetworkUtil: getNetworkState dev
10:32:59.007594 11844 13664 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:32:59.007743 11844 13664 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "INSERT OR REPLACE INTO "CONFIG_DATA" ("_id","TYPE","JSON") VALUES (?,?,?)" took 0.000 ms
10:32:59.008766 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_MSG_SEEK_COMPLETE
10:32:59.008783 11844 13646 D IJKMEDIA: FFP_MSG_SEEK_COMPLETE:
10:32:59.008813 11844 13646 I player_log_tag: message = 4, arg1 = 0, arg2 = 0
10:32:59.009277 11844 13648 I IJKMEDIA: read over from cache queue 
10:32:59.012322 11844 13648 D IJKMEDIA: ffp_toggle_buffering_l: end
10:32:59.012509 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_MSG_BUFFERING_END
10:32:59.012529 11844 13646 D IJKMEDIA: FFP_MSG_BUFFERING_END:
10:32:59.012547 11844 13646 I player_log_tag: message = 200, arg1 = 702, arg2 = 1
10:32:59.013465 11844 12255 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:32:59.013550 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  playImpl info
10:32:59.013591 11844 11844 D AudioZoneSwitch: getCurZoneIdByUid uid 10040
10:32:59.013611 11844 12255 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "INSERT INTO "REPORT_DATA" ("_id","TYPE","SEND_STR") VALUES (?,?,?)" took 0.000 ms
10:32:59.014362 11844 11844 D AudioZoneSwitch: getZoneIdByDisplayId displayId 0
10:32:59.014843 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] setZoneId  lastZoneId 0  ,displayId 0  ,getZoneId 0
10:32:59.015812 11844 13664 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "COMMIT;" took 8.000 ms
10:32:59.015988 11844 13664 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "SELECT COUNT(*) FROM "CONFIG_DATA"" took 0.000 ms
10:32:59.016719 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [AudioFocusManager user0] request in mHasFocus = true,requestResult=1
10:32:59.016765 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/audioSuccess
10:32:59.016977 11844 11844 I player_log_tag: PlayerManager.play->fromUser = true
10:32:59.017013 11844 11844 I player_log_tag: PlayerCustomizeManager.disposePlay->isDispose = false
10:32:59.017032 11844 11844 I player_log_tag: PlayerService.checkAudioFocus->isLoseAudioFocus = true
10:32:59.017044 11844 11844 I player_log_tag: PlayerService.checkAudioFocus->no need
10:32:59.017060 11844 11844 I player_log_tag: IjkMediaPlayer.play->play
10:32:59.017082 11844 11844 D IJKMEDIA: FUNC >> IjkMediaPlayer_start() 
10:32:59.017099 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref++: 3 
10:32:59.017107 11844 11844 D IJKMEDIA: ijkmp_start()
10:32:59.017114 11844 11844 D IJKMEDIA: ijkmp_start_l()
10:32:59.017121 11844 11844 D IJKMEDIA: ikjmp_chkst_start_l--->mp_state:5
10:32:59.017152 11844 11844 D IJKMEDIA: ijkmp_start()=0
10:32:59.017169 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_REQ_START
10:32:59.017205 11844 13646 D IJKMEDIA: ikjmp_chkst_start_l--->mp_state:5
10:32:59.017225 11844 13646 D IJKMEDIA: ijkmp_get_msg: FFP_REQ_START: start on fly
10:32:59.017243 11844 13646 D IJKMEDIA: ijkmp_change_state_l:4
10:32:59.017256 11844 13646 D IJKMEDIA: FFP_MSG_STARTED:
10:32:59.017291 11844 11844 V IJKMEDIA: mp[0xb400007b6c89f610] ref--: 2 
10:32:59.017320 11844 11844 D IJKMEDIA: FUNC << IjkMediaPlayer_start() 
10:32:59.017328 11844 13646 I player_log_tag: message = 10, arg1 = 0, arg2 = 0
10:32:59.017405 11844 13656 I PlayerBase: baseStart() piid=33183, uid=10040, pid=11844, name=com.voyah.cockpit.voyahmusic, Callers=android.media.AudioTrack.startImpl:2957 android.media.AudioTrack.play:2928 <bottom of call stack> 
10:32:59.017830 11844 13665 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:32:59.017920 11844 13665 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "INSERT OR REPLACE INTO "CONFIG_DATA" ("_id","TYPE","JSON") VALUES (?,?,?)" took 0.000 ms
10:32:59.023398 11844 13646 D IJKMEDIA: FFP_MSG_AUDIO_SEEK_RENDERING_START:
10:32:59.023434 11844 13646 I player_log_tag: message = 200, arg1 = 10009, arg2 = 1
10:32:59.025929 11844 12255 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "COMMIT;" took 12.000 ms
10:32:59.026109 11844 12255 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:32:59.027310 11844 13665 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "COMMIT;" took 10.000 ms
10:32:59.027391 11844 13665 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportConfig.db: "SELECT COUNT(*) FROM "CONFIG_DATA"" took 0.000 ms
10:32:59.029322 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onplaystatechanged
10:32:59.029674 11844 11844 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0] onPlayerPlayingPlayItem{audioId=1015809379411, playUrl='https://iovimage.radio.cn/mz/mp3_32/202508/1c88e323-983a-4a4b-836d-1b7c28d2eebf.mp3', playUrlId='9223371021045396396', position=6084, buyType=0, buyStatus=0, audition=0, vip=0, fine=0, duration=128444, mDataMap={}, listenCount=9328049, payMethod=[com.kaolafm.opensdk.api.media.model.PayMethod@769d1d1], type=0}
10:32:59.029696 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onplaystatechanged
10:32:59.029877 11844 11844 I IPCServiceEngine: notify: url = yt_music/ytloginsm/onopenvip userId = 0
10:32:59.030012 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=6084;duration=128444
10:32:59.030040 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mAudioPlayedTime=5082
10:32:59.030053 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=6084
10:32:59.030078 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:32:59.030973 11844 12387 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:32:59.031245 11844 12387 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "INSERT INTO "REPORT_DATA" ("_id","TYPE","SEND_STR") VALUES (?,?,?)" took 0.000 ms
10:32:59.031576 11844 11875 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:59.031625 11844 12390 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:59.031649 11844 12390 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:59.031661 11844 12390 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:59.032107 11844 13664 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:32:59.032155 11844 13664 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:32:59.032166 11844 13664 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:32:59.032179 11844 13664 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:59.032275 11844 13664 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:59.032284 11844 13664 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:59.032303 11844 13664 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:59.032538 11844 12390 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:32:59.032623 11844 12390 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:32:59.032636 11844 12390 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:32:59.032678 11844 13703 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM yt_play_history_list WHERE audioId = ?" took 0.000 ms
10:32:59.032704 11844 13703 D SQLiteCursor: received count(*) from native_fill_window: 1
10:32:59.032735 11844 12390 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:32:59.033226 11844 13703 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:32:59.033393 11844 13703 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "UPDATE OR ABORT `yt_play_history_list` SET `id` = ?,`mediaId` = ?,`audioId` = ?,`progress` = ? WHERE `id` = ?" took 0.000 ms
10:32:59.033526 11844 11875 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtLoginSM user0]  login = false 未登录
10:32:59.033702 11844 13664 D okhttp.Http2: >> 0x00000023   377 HEADERS       END_HEADERS
10:32:59.033803 11844 13664 D okhttp.Http2: >> 0x00000023   149 DATA          END_STREAM
10:32:59.034994 11844 12390 D okhttp.Http2: >> 0x00000025   377 HEADERS       END_HEADERS
10:32:59.035110 11844 13703 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 2.000 ms
10:32:59.035141 11844 12390 D okhttp.Http2: >> 0x00000025   152 DATA          END_STREAM
10:32:59.036178 11844 13662 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 1.000 ms
10:32:59.036369 11844 13662 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM room_table_modification_log WHERE invalidated = 1;" took 0.000 ms
10:32:59.036399 11844 13662 D SQLiteCursor: received count(*) from native_fill_window: 0
10:32:59.036464 11844 13662 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 0.000 ms
10:32:59.046530 11844 11875 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:59.048206 11844 11875 I [VoyahMusic 10.1.5.20250819185918.a8ba89b91 H56D_SOP_release]: [YtMusicPlayerSM user0]  getMediaInfoImpl
10:32:59.049153 11844 12387 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "COMMIT;" took 18.000 ms
10:32:59.049272 11844 12387 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:32:59.072448 11844 12406 D okhttp.Http2: << 0x00000023   193 HEADERS       END_HEADERS
10:32:59.073170 11844 12406 D okhttp.Http2: << 0x00000023    96 DATA          
10:32:59.073528 11844 12406 D okhttp.Http2: << 0x00000023     0 DATA          END_STREAM
10:32:59.114321 11844 12406 D okhttp.Http2: << 0x00000025   193 HEADERS       END_HEADERS
10:32:59.115103 11844 12406 D okhttp.Http2: << 0x00000025    97 DATA          
10:32:59.115303 11844 12406 D okhttp.Http2: << 0x00000025     0 DATA          END_STREAM
10:32:59.116077 11844 12390 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:32:59.116186 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:32:59.116248 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:32:59.116277 11844 12405 D okhttp.TaskRunner: Q10001 finished run in  99 µs: OkHttp ConnectionPool
10:32:59.129818 11844 11872 W System  : A resource failed to call ContentProviderClient.close. 
10:32:59.129957 11844 11872 W System  : A resource failed to call ContentProviderClient.close. 
10:32:59.130197 11844 11872 W System  : A resource failed to call ContentProviderClient.close. 
10:32:59.130282 11844 11872 W System  : A resource failed to call ContentProviderClient.close. 
10:32:59.183351 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=5924;duration=128444
10:32:59.183395 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=5924
10:32:59.183432 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:32:59.312391 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=6046;duration=128444
10:32:59.312452 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mAudioPlayedTime=5204
10:32:59.312479 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=6046
10:32:59.312595 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:33:00.010645 11844 13699 I NetworkUtil: 安卓系统下行字节数Rx = 196kbs，上行字节数Tx = 199kbs
10:33:00.010699 11844 13699 I NetworkUtil: 所在进程下行字节数Rx = 26kbs，上行字节数Tx = 1kbs
10:33:00.263342 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=7007;duration=128444
10:33:00.263426 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mAudioPlayedTime=6165
10:33:00.263443 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=7007
10:33:00.263469 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:33:01.012835 11844 13699 I NetworkUtil: 安卓系统下行字节数Rx = 3kbs，上行字节数Tx = 6kbs
10:33:01.012880 11844 13699 I NetworkUtil: 所在进程下行字节数Rx = 0kbs，上行字节数Tx = 0kbs
10:33:01.342738 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->progress=8087;duration=128444
10:33:01.342772 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mAudioPlayedTime=7245
10:33:01.342787 11844 11844 I player_log_tag: SDKReportManager$1.onProgress->mCurrentPosition=8087
10:33:01.342811 11844 11844 I aaccdd1 : ipc:: onNotify: url = yt_music/mediabridgesm/onprogresschanged
10:33:01.343285 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM yt_play_history_list WHERE audioId = ?" took 0.000 ms
10:33:01.343305 11844 13661 D SQLiteCursor: received count(*) from native_fill_window: 1
10:33:01.343376 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:33:01.343418 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "UPDATE OR ABORT `yt_play_history_list` SET `id` = ?,`mediaId` = ?,`audioId` = ?,`progress` = ? WHERE `id` = ?" took 0.000 ms
10:33:01.345676 11844 12390 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:33:01.345699 11844 12390 D CommonParamModule: ensureValidAccessToken: 当前 openId=ie70682025011610029074, access_token=null, userId=null
10:33:01.345711 11844 12390 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:33:01.345724 11844 12390 D CommonParamModule: provideKaolaParams: 当前的 openId: ie70682025011610029074
10:33:01.345835 11844 12390 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:33:01.345848 11844 12390 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:33:01.345905 11844 13661 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 2.000 ms
10:33:01.345953 11844 12390 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {capabilities=NEW_DOMAIN_SUPPORTTED,PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=, openid=ie70682025011610029074, packagename=com.voyah.cockpit.voyahmusic, sign=a1f93eca8683c536b0c5a96f6f98ee2c, channel=com.voyah.cockpit.voyahmusic, deviceid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, version=10.1.5.20250819185918.a8ba89b91, sdkversion=********, appid=ie7068, udid=10e7ae2adaee1db1558131f53daf536ebb46cbf1690693de0e989d901c23f504, lat=}
10:33:01.346288 11844 13663 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "BEGIN IMMEDIATE;" took 0.000 ms
10:33:01.346382 11844 13663 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "SELECT * FROM room_table_modification_log WHERE invalidated = 1;" took 0.000 ms
10:33:01.346549 11844 13663 D SQLiteCursor: received count(*) from native_fill_window: 0
10:33:01.346605 11844 13663 V SQLiteTime: /data/user/0/com.voyah.cockpit.voyahmusic/databases/yt_play_history_list.db: "COMMIT;" took 0.000 ms
10:33:01.348869 11844 12390 D okhttp.Http2: >> 0x00000027   377 HEADERS       END_HEADERS
10:33:01.349379 11844 12390 D okhttp.Http2: >> 0x00000027   152 DATA          END_STREAM
10:33:01.422727 11844 12406 D okhttp.Http2: << 0x00000027   193 HEADERS       END_HEADERS
10:33:01.423077 11844 12406 D okhttp.Http2: << 0x00000027    97 DATA          
10:33:01.423924 11844 12406 D okhttp.Http2: << 0x00000027     0 DATA          END_STREAM
10:33:01.424128 11844 12390 D okhttp.TaskRunner: Q10001 scheduled after   0 µs: OkHttp ConnectionPool
10:33:01.424271 11844 12405 D okhttp.TaskRunner: Q10001 starting              : OkHttp ConnectionPool
10:33:01.424330 11844 12405 D okhttp.TaskRunner: Q10001 run again after  10 s : OkHttp ConnectionPool
10:33:01.424369 11844 12405 D okhttp.TaskRunner: Q10001 finished run in 105 µs: OkHttp ConnectionPool
10:33:02.014661 11844 13699 I NetworkUtil: 安卓系统下行字节数Rx = 196kbs，上行字节数Tx = 199kbs
10:33:02.014697 11844 13699 I NetworkUtil: 所在进程下行字节数Rx = 0kbs，上行字节数Tx = 0kbs