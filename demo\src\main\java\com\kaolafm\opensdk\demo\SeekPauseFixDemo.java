package com.kaolafm.opensdk.demo;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * Seek过程中暂停状态管理修复功能演示
 * 
 * 这个演示展示了修复后的播放器如何正确处理seek过程中的暂停操作
 */
public class SeekPauseFixDemo {
    
    private static final String TAG = "SeekPauseFixDemo";
    private PlayerManager playerManager;
    private Handler mainHandler;
    
    public SeekPauseFixDemo(Context context) {
        playerManager = PlayerManager.getInstance();
        playerManager.init(context);
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 添加状态监听器来观察播放器状态变化
        playerManager.addPlayerStateListener(new DemoStateListener());
    }
    
    /**
     * 演示场景1：正常的seek操作（没有暂停干扰）
     */
    public void demonstrateNormalSeek() {
        Log.i(TAG, "=== 演示场景1：正常seek操作 ===");
        
        // 开始播放
        // playerManager.start(builder); // 这里需要实际的播放项目
        
        // 等待播放开始后进行seek
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "执行seek操作到5秒位置");
            playerManager.seek(5000);
        }, 1000);
        
        // seek完成后应该继续播放
        Log.i(TAG, "预期结果：seek完成后继续播放");
    }
    
    /**
     * 演示场景2：seek过程中用户暂停（修复的核心场景）
     */
    public void demonstrateSeekWithUserPause() {
        Log.i(TAG, "=== 演示场景2：seek过程中用户暂停 ===");
        
        // 开始播放
        // playerManager.start(builder);
        
        // 开始seek操作
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "执行seek操作到10秒位置");
            playerManager.seek(10000);
        }, 1000);
        
        // 在seek过程中用户点击暂停
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "用户在seek过程中点击暂停");
            playerManager.pause(true); // fromUser = true
        }, 1500);
        
        // seek完成后应该保持暂停状态（这是修复的关键）
        Log.i(TAG, "预期结果：seek完成后保持暂停状态，不会自动播放");
    }
    
    /**
     * 演示场景3：seek过程中系统暂停（比如音频焦点丢失）
     */
    public void demonstrateSeekWithSystemPause() {
        Log.i(TAG, "=== 演示场景3：seek过程中系统暂停 ===");
        
        // 开始播放
        // playerManager.start(builder);
        
        // 开始seek操作
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "执行seek操作到15秒位置");
            playerManager.seek(15000);
        }, 1000);
        
        // 系统暂停（比如音频焦点丢失）
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "系统暂停（音频焦点丢失等）");
            playerManager.pause(false); // fromUser = false
        }, 1500);
        
        // seek完成后的行为取决于音频焦点恢复等系统逻辑
        Log.i(TAG, "预期结果：系统暂停不影响seek完成后的自动播放逻辑");
    }
    
    /**
     * 演示场景4：连续seek操作
     */
    public void demonstrateMultipleSeeks() {
        Log.i(TAG, "=== 演示场景4：连续seek操作 ===");
        
        // 开始播放
        // playerManager.start(builder);
        
        // 第一次seek
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "第一次seek到5秒位置");
            playerManager.seek(5000);
        }, 1000);
        
        // 第二次seek（在第一次完成前）
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "第二次seek到20秒位置");
            playerManager.seek(20000);
        }, 1500);
        
        // 在第二次seek过程中暂停
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "在第二次seek过程中用户暂停");
            playerManager.pause(true);
        }, 2000);
        
        Log.i(TAG, "预期结果：最终seek完成后保持暂停状态");
    }
    
    /**
     * 演示场景5：seek完成后立即进行其他操作
     */
    public void demonstrateOperationsAfterSeek() {
        Log.i(TAG, "=== 演示场景5：seek完成后立即操作 ===");
        
        // 开始播放
        // playerManager.start(builder);
        
        // seek操作
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "执行seek操作");
            playerManager.seek(8000);
        }, 1000);
        
        // seek过程中暂停
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "seek过程中暂停");
            playerManager.pause(true);
        }, 1500);
        
        // seek完成后立即播放
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "seek完成后用户立即点击播放");
            playerManager.play(true);
        }, 3000);
        
        Log.i(TAG, "预期结果：播放操作正常执行，覆盖之前的暂停状态");
    }
    
    /**
     * 状态监听器 - 用于观察播放器状态变化
     */
    private class DemoStateListener implements IPlayerStateListener {
        
        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            Log.i(TAG, "🎵 播放器开始播放: " + (playItem != null ? playItem.getAudioId() : "null"));
        }
        
        @Override
        public void onPlayerPaused(PlayItem playItem) {
            Log.i(TAG, "⏸️ 播放器暂停: " + (playItem != null ? playItem.getAudioId() : "null"));
        }
        
        @Override
        public void onSeekStart(PlayItem playItem) {
            Log.i(TAG, "🔄 Seek开始: " + (playItem != null ? playItem.getAudioId() : "null"));
        }
        
        @Override
        public void onSeekComplete(PlayItem playItem) {
            Log.i(TAG, "✅ Seek完成: " + (playItem != null ? playItem.getAudioId() : "null"));
        }
        
        @Override
        public void onProgress(PlayItem playItem, long progress, long total) {
            // 进度更新，这里可以观察播放位置变化
            if (progress % 1000 == 0) { // 每秒打印一次
                Log.d(TAG, "播放进度: " + progress + "/" + total);
            }
        }
        
        // 其他方法的空实现
        @Override public void onIdle(PlayItem playItem) {}
        @Override public void onPlayerPreparing(PlayItem playItem) {}
        @Override public void onPlayerFailed(PlayItem playItem, int what, int extra) {}
        @Override public void onPlayerEnd(PlayItem playItem) {}
        @Override public void onBufferingStart(PlayItem playItem) {}
        @Override public void onBufferingEnd(PlayItem playItem) {}
        @Override public void onDownloadProgress(PlayItem playItem, long progress, long total) {}
    }
    
    /**
     * 运行所有演示场景
     */
    public void runAllDemonstrations() {
        Log.i(TAG, "开始运行Seek暂停修复功能演示");
        
        demonstrateNormalSeek();
        
        mainHandler.postDelayed(this::demonstrateSeekWithUserPause, 5000);
        
        mainHandler.postDelayed(this::demonstrateSeekWithSystemPause, 10000);
        
        mainHandler.postDelayed(this::demonstrateMultipleSeeks, 15000);
        
        mainHandler.postDelayed(this::demonstrateOperationsAfterSeek, 20000);
        
        mainHandler.postDelayed(() -> {
            Log.i(TAG, "所有演示场景完成");
        }, 25000);
    }
}
